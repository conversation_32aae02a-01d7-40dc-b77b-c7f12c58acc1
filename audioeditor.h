#ifndef AUDIOEDITOR_H
#define AUDIOEDITOR_H

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QMenuBar>
#include <QStatusBar>
#include <QAction>
#include <QFileDialog>
#include <QMessageBox>
#include <QSplitter>
#include <QScrollArea>
#include <QTimer>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <QPixmap>

class AudioTimelineWidget;

class AudioEditor : public QMainWindow
{
    Q_OBJECT

public:
    explicit AudioEditor(const QString &filePath, QWidget *parent = nullptr);
    ~AudioEditor();

private slots:
    void playPause();
    void stop();
    void cutSelection();
    void deleteSelection();
    void splitAtPosition();
    void saveFile();
    void saveAsFile();
    void exportFile();
    void closeEditor();
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onPlaybackStateChanged();
    void seekToPosition(int position);
    void setVolume(int volume);
    void zoomIn();
    void zoomOut();
    void resetZoom();
    void selectAll();
    void onSelectionChanged(qint64 start, qint64 end);

private:
    void setupUI();
    void setupMenuBar();
    void setupToolbar();
    void setupStatusBar();
    void setupTrackInfo();
    void setupTimeline();
    void setupControls();
    void setupEditControls();
    void loadAudioFile(const QString &filePath);
    void updatePlayPauseButton();
    void updateTimeLabels();
    void updateAlbumArt();
    void setButtonIcon(QPushButton *button, const QString &iconName);
    QString formatTime(qint64 milliseconds);
    bool hasUnsavedChanges() const;
    void setUnsavedChanges(bool hasChanges);

    // Audio file info
    QString m_filePath;
    QString m_fileName;
    QString m_trackTitle;
    QString m_trackArtist;
    qint64 m_duration;
    bool m_hasUnsavedChanges;

    // Audio playback
    QMediaPlayer *m_mediaPlayer;
    QAudioOutput *m_audioOutput;

    // UI Components
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QSplitter *m_mainSplitter;
    
    // Track info section
    QWidget *m_trackInfoWidget;
    QLabel *m_albumArtLabel;
    QLabel *m_trackTitleLabel;
    QLabel *m_trackArtistLabel;
    QLabel *m_fileNameLabel;
    
    // Timeline section
    QScrollArea *m_timelineScrollArea;
    AudioTimelineWidget *m_timelineWidget;
    
    // Control section
    QWidget *m_controlsWidget;
    QPushButton *m_playPauseButton;
    QPushButton *m_stopButton;
    QSlider *m_positionSlider;
    QLabel *m_currentTimeLabel;
    QLabel *m_totalTimeLabel;
    QSlider *m_volumeSlider;
    QLabel *m_volumeLabel;
    
    // Edit controls
    QWidget *m_editControlsWidget;
    QPushButton *m_cutButton;
    QPushButton *m_deleteButton;
    QPushButton *m_splitButton;
    QPushButton *m_zoomInButton;
    QPushButton *m_zoomOutButton;
    QPushButton *m_resetZoomButton;
    QLabel *m_selectionLabel;
    
    // Menu actions
    QAction *m_saveAction;
    QAction *m_saveAsAction;
    QAction *m_exportAction;
    QAction *m_closeAction;
    QAction *m_cutAction;
    QAction *m_deleteAction;
    QAction *m_splitAction;
    QAction *m_selectAllAction;
    QAction *m_zoomInAction;
    QAction *m_zoomOutAction;
    QAction *m_resetZoomAction;
};

#endif // AUDIOEDITOR_H
