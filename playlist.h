#ifndef PLAYLIST_H
#define PLAYLIST_H

#include <QObject>
#include <QStringList>
#include <QFileInfo>

class Playlist : public QObject
{
    Q_OBJECT

public:
    explicit Playlist(QObject *parent = nullptr);
    ~Playlist();

    // Playlist management
    void addTrack(const QString &filePath);
    void addTracks(const QStringList &filePaths);
    void removeTrack(int index);
    void removeTrack(const QString &filePath);
    void clear();
    void moveTrack(int from, int to);
    
    // Navigation
    QString currentTrack() const;
    QString nextTrack();
    QString previousTrack();
    void setCurrentIndex(int index);
    int currentIndex() const;
    
    // Information
    int count() const;
    QStringList tracks() const;
    QString trackAt(int index) const;
    bool isEmpty() const;
    bool contains(const QString &filePath) const;
    int indexOf(const QString &filePath) const;
    
    // Utility
    QString getTrackTitle(const QString &filePath) const;
    QString getTrackArtist(const QString &filePath) const;
    qint64 getTrackDuration(const QString &filePath) const;
    QString formatDuration(qint64 milliseconds) const;

signals:
    void trackAdded(const QString &filePath);
    void trackRemoved(int index);
    void tracksCleared();
    void currentTrackChanged(const QString &filePath);
    void playlistChanged();

private:
    bool isValidAudioFile(const QString &filePath) const;
    QString extractTitleFromFileName(const QString &filePath) const;

    QStringList m_tracks;
    int m_currentIndex;
    QStringList m_supportedFormats;
};

#endif // PLAYLIST_H
