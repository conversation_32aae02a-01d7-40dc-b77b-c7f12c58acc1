#include "audioeditor.h"
#include "audiotimelinewidget.h"
#include <QApplication>
#include <QFileInfo>
#include <QDir>
#include <QCloseEvent>

AudioEditor::AudioEditor(const QString &filePath, QWidget *parent)
    : QMainWindow(parent)
    , m_filePath(filePath)
    , m_duration(0)
    , m_hasUnsavedChanges(false)
    , m_mediaPlayer(nullptr)
    , m_audioOutput(nullptr)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_mainSplitter(nullptr)
    , m_trackInfoWidget(nullptr)
    , m_albumArtLabel(nullptr)
    , m_trackTitleLabel(nullptr)
    , m_trackArtistLabel(nullptr)
    , m_fileNameLabel(nullptr)
    , m_timelineScrollArea(nullptr)
    , m_timelineWidget(nullptr)
    , m_controlsWidget(nullptr)
    , m_playPauseButton(nullptr)
    , m_stopButton(nullptr)
    , m_positionSlider(nullptr)
    , m_currentTimeLabel(nullptr)
    , m_totalTimeLabel(nullptr)
    , m_volumeSlider(nullptr)
    , m_volumeLabel(nullptr)
    , m_editControlsWidget(nullptr)
    , m_cutButton(nullptr)
    , m_deleteButton(nullptr)
    , m_splitButton(nullptr)
    , m_zoomInButton(nullptr)
    , m_zoomOutButton(nullptr)
    , m_resetZoomButton(nullptr)
    , m_selectionLabel(nullptr)
{
    setWindowTitle("Audio Editor - " + QFileInfo(filePath).fileName());
    setMinimumSize(1000, 700);
    resize(1200, 800);
    
    // Set up dark theme
    setStyleSheet(
        "QMainWindow {"
        "    background-color: #1a1a1a;"
        "    color: #ffffff;"
        "}"
        "QMenuBar {"
        "    background-color: #2a2a2a;"
        "    color: #ffffff;"
        "    border-bottom: 1px solid #555555;"
        "}"
        "QMenuBar::item {"
        "    background-color: transparent;"
        "    padding: 4px 8px;"
        "}"
        "QMenuBar::item:selected {"
        "    background-color: #42a5f5;"
        "}"
        "QStatusBar {"
        "    background-color: #2a2a2a;"
        "    color: #cccccc;"
        "    border-top: 1px solid #555555;"
        "}"
    );
    
    setupUI();
    setupMenuBar();
    setupStatusBar();
    loadAudioFile(filePath);
}

AudioEditor::~AudioEditor()
{
    if (m_mediaPlayer) {
        m_mediaPlayer->stop();
    }
}

void AudioEditor::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    
    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Vertical);
    m_mainLayout->addWidget(m_mainSplitter);
    
    // Track info section
    setupTrackInfo();
    
    // Timeline section
    setupTimeline();
    
    // Controls section
    setupControls();
    
    // Edit controls section
    setupEditControls();
    
    // Set splitter proportions
    m_mainSplitter->setSizes({150, 300, 100, 80});
    
    // Initialize audio player
    m_mediaPlayer = new QMediaPlayer(this);
    m_audioOutput = new QAudioOutput(this);
    m_mediaPlayer->setAudioOutput(m_audioOutput);
    
    // Connect signals
    connect(m_mediaPlayer, &QMediaPlayer::positionChanged, this, &AudioEditor::onPositionChanged);
    connect(m_mediaPlayer, &QMediaPlayer::durationChanged, this, &AudioEditor::onDurationChanged);
    connect(m_mediaPlayer, &QMediaPlayer::playbackStateChanged, this, &AudioEditor::onPlaybackStateChanged);
}

void AudioEditor::setupTrackInfo()
{
    m_trackInfoWidget = new QWidget;
    m_trackInfoWidget->setStyleSheet(
        "QWidget {"
        "    background-color: #2a2a2a;"
        "    border-radius: 5px;"
        "    padding: 10px;"
        "}"
    );
    
    QHBoxLayout *infoLayout = new QHBoxLayout(m_trackInfoWidget);
    
    // Album art
    m_albumArtLabel = new QLabel;
    m_albumArtLabel->setFixedSize(120, 120);
    m_albumArtLabel->setAlignment(Qt::AlignCenter);
    m_albumArtLabel->setStyleSheet(
        "QLabel {"
        "    border: 2px solid #555555;"
        "    border-radius: 5px;"
        "    background-color: #3a3a3a;"
        "}"
    );
    m_albumArtLabel->setScaledContents(true);
    infoLayout->addWidget(m_albumArtLabel);
    
    // Track details
    QVBoxLayout *detailsLayout = new QVBoxLayout;
    
    m_trackTitleLabel = new QLabel("Track Title");
    m_trackTitleLabel->setStyleSheet("font-size: 16px; font-weight: bold; color: #ffffff;");
    detailsLayout->addWidget(m_trackTitleLabel);
    
    m_trackArtistLabel = new QLabel("Artist Name");
    m_trackArtistLabel->setStyleSheet("font-size: 14px; color: #cccccc;");
    detailsLayout->addWidget(m_trackArtistLabel);
    
    m_fileNameLabel = new QLabel("filename.mp3");
    m_fileNameLabel->setStyleSheet("font-size: 12px; color: #999999;");
    detailsLayout->addWidget(m_fileNameLabel);
    
    detailsLayout->addStretch();
    infoLayout->addLayout(detailsLayout);
    infoLayout->addStretch();
    
    m_mainSplitter->addWidget(m_trackInfoWidget);
}

void AudioEditor::setupTimeline()
{
    m_timelineScrollArea = new QScrollArea;
    m_timelineScrollArea->setStyleSheet(
        "QScrollArea {"
        "    background-color: #2a2a2a;"
        "    border: 1px solid #555555;"
        "    border-radius: 5px;"
        "}"
    );
    
    m_timelineWidget = new AudioTimelineWidget;
    m_timelineScrollArea->setWidget(m_timelineWidget);
    m_timelineScrollArea->setWidgetResizable(true);
    m_timelineScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_timelineScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    
    connect(m_timelineWidget, &AudioTimelineWidget::positionClicked, 
            this, [this](qint64 position) {
                m_mediaPlayer->setPosition(position);
            });
    connect(m_timelineWidget, &AudioTimelineWidget::selectionChanged,
            this, &AudioEditor::onSelectionChanged);
    connect(m_timelineWidget, &AudioTimelineWidget::positionClicked,
            this, &AudioEditor::onPositionClicked);
    connect(m_timelineWidget, &AudioTimelineWidget::segmentSelected,
            this, &AudioEditor::onSegmentSelected);
    connect(m_timelineWidget, &AudioTimelineWidget::splitMarkerAdded,
            this, &AudioEditor::onSplitMarkerAdded);
    connect(m_timelineWidget, &AudioTimelineWidget::splitMarkerRemoved,
            this, &AudioEditor::onSplitMarkerRemoved);
    
    m_mainSplitter->addWidget(m_timelineScrollArea);
}

void AudioEditor::setupControls()
{
    m_controlsWidget = new QWidget;
    m_controlsWidget->setStyleSheet(
        "QWidget {"
        "    background-color: #2a2a2a;"
        "    border-radius: 5px;"
        "    padding: 10px;"
        "}"
    );
    
    QHBoxLayout *controlsLayout = new QHBoxLayout(m_controlsWidget);
    
    // Playback controls
    m_playPauseButton = new QPushButton;
    setButtonIcon(m_playPauseButton, "play");
    connect(m_playPauseButton, &QPushButton::clicked, this, &AudioEditor::playPause);
    controlsLayout->addWidget(m_playPauseButton);
    
    m_stopButton = new QPushButton;
    setButtonIcon(m_stopButton, "stop");
    connect(m_stopButton, &QPushButton::clicked, this, &AudioEditor::stop);
    controlsLayout->addWidget(m_stopButton);
    
    controlsLayout->addSpacing(20);
    
    // Position controls
    m_currentTimeLabel = new QLabel("0:00");
    m_currentTimeLabel->setStyleSheet("color: #cccccc; font-family: monospace;");
    controlsLayout->addWidget(m_currentTimeLabel);
    
    m_positionSlider = new QSlider(Qt::Horizontal);
    m_positionSlider->setStyleSheet(
        "QSlider::groove:horizontal {"
        "    border: 1px solid #555555;"
        "    height: 6px;"
        "    background: #3a3a3a;"
        "    border-radius: 3px;"
        "}"
        "QSlider::handle:horizontal {"
        "    background: #42a5f5;"
        "    border: 1px solid #1976d2;"
        "    width: 16px;"
        "    margin: -5px 0;"
        "    border-radius: 8px;"
        "}"
        "QSlider::sub-page:horizontal {"
        "    background: #42a5f5;"
        "    border-radius: 3px;"
        "}"
    );
    connect(m_positionSlider, &QSlider::sliderPressed, [this]() {
        m_mediaPlayer->pause();
    });
    connect(m_positionSlider, &QSlider::sliderReleased, [this]() {
        m_mediaPlayer->setPosition(m_positionSlider->value());
    });
    controlsLayout->addWidget(m_positionSlider, 1);
    
    m_totalTimeLabel = new QLabel("0:00");
    m_totalTimeLabel->setStyleSheet("color: #cccccc; font-family: monospace;");
    controlsLayout->addWidget(m_totalTimeLabel);
    
    controlsLayout->addSpacing(20);
    
    // Volume controls
    QLabel *volumeIcon = new QLabel("🔊");
    controlsLayout->addWidget(volumeIcon);
    
    m_volumeSlider = new QSlider(Qt::Horizontal);
    m_volumeSlider->setRange(0, 100);
    m_volumeSlider->setValue(70);
    m_volumeSlider->setMaximumWidth(100);
    m_volumeSlider->setStyleSheet(m_positionSlider->styleSheet());
    connect(m_volumeSlider, &QSlider::valueChanged, this, &AudioEditor::setVolume);
    controlsLayout->addWidget(m_volumeSlider);
    
    m_volumeLabel = new QLabel("70%");
    m_volumeLabel->setStyleSheet("color: #cccccc; font-family: monospace;");
    m_volumeLabel->setMinimumWidth(35);
    controlsLayout->addWidget(m_volumeLabel);
    
    m_mainSplitter->addWidget(m_controlsWidget);
}

void AudioEditor::setupEditControls()
{
    m_editControlsWidget = new QWidget;
    m_editControlsWidget->setStyleSheet(
        "QWidget {"
        "    background-color: #2a2a2a;"
        "    border-radius: 5px;"
        "    padding: 10px;"
        "}"
    );
    
    QHBoxLayout *editLayout = new QHBoxLayout(m_editControlsWidget);
    
    // Edit buttons
    m_cutButton = new QPushButton("Cut");
    m_cutButton->setEnabled(false);
    connect(m_cutButton, &QPushButton::clicked, this, &AudioEditor::cutSelection);
    editLayout->addWidget(m_cutButton);
    
    m_deleteButton = new QPushButton("Delete");
    m_deleteButton->setEnabled(false);
    connect(m_deleteButton, &QPushButton::clicked, this, &AudioEditor::deleteSelection);
    editLayout->addWidget(m_deleteButton);
    
    m_splitButton = new QPushButton("Split");
    connect(m_splitButton, &QPushButton::clicked, this, &AudioEditor::splitAtPosition);
    editLayout->addWidget(m_splitButton);
    
    editLayout->addSpacing(20);
    
    // Zoom controls
    m_zoomInButton = new QPushButton("Zoom In");
    connect(m_zoomInButton, &QPushButton::clicked, this, &AudioEditor::zoomIn);
    editLayout->addWidget(m_zoomInButton);
    
    m_zoomOutButton = new QPushButton("Zoom Out");
    connect(m_zoomOutButton, &QPushButton::clicked, this, &AudioEditor::zoomOut);
    editLayout->addWidget(m_zoomOutButton);
    
    m_resetZoomButton = new QPushButton("Reset Zoom");
    connect(m_resetZoomButton, &QPushButton::clicked, this, &AudioEditor::resetZoom);
    editLayout->addWidget(m_resetZoomButton);
    
    editLayout->addStretch();
    
    // Selection info
    m_selectionLabel = new QLabel("No selection");
    m_selectionLabel->setStyleSheet("color: #cccccc;");
    editLayout->addWidget(m_selectionLabel);
    
    m_mainSplitter->addWidget(m_editControlsWidget);
}

void AudioEditor::setupMenuBar()
{
    // File menu
    QMenu *fileMenu = menuBar()->addMenu("&File");

    m_saveAction = new QAction("&Save", this);
    m_saveAction->setShortcut(QKeySequence::Save);
    connect(m_saveAction, &QAction::triggered, this, &AudioEditor::saveFile);
    fileMenu->addAction(m_saveAction);

    m_saveAsAction = new QAction("Save &As...", this);
    m_saveAsAction->setShortcut(QKeySequence::SaveAs);
    connect(m_saveAsAction, &QAction::triggered, this, &AudioEditor::saveAsFile);
    fileMenu->addAction(m_saveAsAction);

    m_exportAction = new QAction("&Export...", this);
    m_exportAction->setShortcut(QKeySequence("Ctrl+Shift+E"));
    connect(m_exportAction, &QAction::triggered, this, &AudioEditor::exportFile);
    fileMenu->addAction(m_exportAction);

    fileMenu->addSeparator();

    m_closeAction = new QAction("&Close", this);
    m_closeAction->setShortcut(QKeySequence::Close);
    connect(m_closeAction, &QAction::triggered, this, &AudioEditor::closeEditor);
    fileMenu->addAction(m_closeAction);

    // Edit menu
    QMenu *editMenu = menuBar()->addMenu("&Edit");

    m_cutAction = new QAction("Cu&t", this);
    m_cutAction->setShortcut(QKeySequence::Cut);
    m_cutAction->setEnabled(false);
    connect(m_cutAction, &QAction::triggered, this, &AudioEditor::cutSelection);
    editMenu->addAction(m_cutAction);

    m_deleteAction = new QAction("&Delete", this);
    m_deleteAction->setShortcut(QKeySequence::Delete);
    m_deleteAction->setEnabled(false);
    connect(m_deleteAction, &QAction::triggered, this, &AudioEditor::deleteSelection);
    editMenu->addAction(m_deleteAction);

    m_splitAction = new QAction("&Split", this);
    m_splitAction->setShortcut(QKeySequence("Ctrl+T"));
    connect(m_splitAction, &QAction::triggered, this, &AudioEditor::splitAtPosition);
    editMenu->addAction(m_splitAction);

    editMenu->addSeparator();

    m_selectAllAction = new QAction("Select &All", this);
    m_selectAllAction->setShortcut(QKeySequence::SelectAll);
    connect(m_selectAllAction, &QAction::triggered, this, &AudioEditor::selectAll);
    editMenu->addAction(m_selectAllAction);

    // View menu
    QMenu *viewMenu = menuBar()->addMenu("&View");

    m_zoomInAction = new QAction("Zoom &In", this);
    m_zoomInAction->setShortcut(QKeySequence::ZoomIn);
    connect(m_zoomInAction, &QAction::triggered, this, &AudioEditor::zoomIn);
    viewMenu->addAction(m_zoomInAction);

    m_zoomOutAction = new QAction("Zoom &Out", this);
    m_zoomOutAction->setShortcut(QKeySequence::ZoomOut);
    connect(m_zoomOutAction, &QAction::triggered, this, &AudioEditor::zoomOut);
    viewMenu->addAction(m_zoomOutAction);

    m_resetZoomAction = new QAction("&Reset Zoom", this);
    m_resetZoomAction->setShortcut(QKeySequence("Ctrl+0"));
    connect(m_resetZoomAction, &QAction::triggered, this, &AudioEditor::resetZoom);
    viewMenu->addAction(m_resetZoomAction);

    // Help menu
    QMenu *helpMenu = menuBar()->addMenu("&Help");

    QAction *helpAction = new QAction("&Usage Instructions", this);
    connect(helpAction, &QAction::triggered, [this]() {
        QMessageBox::information(this, "Audio Editor Usage",
                                "Audio Editor Usage Instructions:\n\n"
                                "• Click to set playhead position\n"
                                "• Ctrl+Click and drag to select audio\n"
                                "• Alt+Click to select segments between split markers\n"
                                "• Right-click to add/remove split markers\n"
                                "• Use Split button or Ctrl+T to add split at current position\n"
                                "• Zoom with mouse wheel or zoom buttons\n"
                                "• Cut/Delete selected audio with buttons or Ctrl+X/Del\n\n"
                                "Split markers create segments that can be individually selected and edited.");
    });
    helpMenu->addAction(helpAction);

    helpMenu->addSeparator();

    QAction *aboutAction = new QAction("&About", this);
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About Audio Editor",
                          "Audio Editor\n\nA simple audio editing application built with Qt.");
    });
    helpMenu->addAction(aboutAction);
}

void AudioEditor::setupStatusBar()
{
    statusBar()->showMessage("Ready");
}

void AudioEditor::loadAudioFile(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    m_fileName = fileInfo.fileName();
    m_fileNameLabel->setText(m_fileName);

    // Load audio file
    m_mediaPlayer->setSource(QUrl::fromLocalFile(filePath));

    // Update album art
    updateAlbumArt();

    // Set initial volume
    setVolume(70);

    statusBar()->showMessage("Audio file loaded: " + m_fileName);
}

void AudioEditor::updateAlbumArt()
{
    // Try to find album art
    QFileInfo audioFile(m_filePath);
    QString audioDir = audioFile.absolutePath();

    QStringList albumArtNames = {
        "cover.jpg", "cover.jpeg", "cover.png",
        "album.jpg", "album.jpeg", "album.png",
        "folder.jpg", "folder.jpeg", "folder.png"
    };

    QString albumArtPath;
    for (const QString &artName : albumArtNames) {
        QString artPath = QDir(audioDir).absoluteFilePath(artName);
        if (QFile::exists(artPath)) {
            albumArtPath = artPath;
            break;
        }
    }

    if (!albumArtPath.isEmpty()) {
        QPixmap albumArt(albumArtPath);
        if (!albumArt.isNull()) {
            QPixmap scaledPixmap = albumArt.scaled(m_albumArtLabel->size(),
                                                  Qt::KeepAspectRatio,
                                                  Qt::SmoothTransformation);
            m_albumArtLabel->setPixmap(scaledPixmap);
            return;
        }
    }

    // Use default album art
    QPixmap defaultArt(":/icons/icons/album-default.svg");
    if (!defaultArt.isNull()) {
        QPixmap scaledPixmap = defaultArt.scaled(m_albumArtLabel->size(),
                                                Qt::KeepAspectRatio,
                                                Qt::SmoothTransformation);
        m_albumArtLabel->setPixmap(scaledPixmap);
    } else {
        m_albumArtLabel->setText("♪");
        m_albumArtLabel->setStyleSheet(m_albumArtLabel->styleSheet() +
                                      " font-size: 24px; color: #42a5f5;");
    }
}

// Slot implementations
void AudioEditor::playPause()
{
    if (m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState) {
        m_mediaPlayer->pause();
    } else {
        m_mediaPlayer->play();
    }
}

void AudioEditor::stop()
{
    m_mediaPlayer->stop();
}

void AudioEditor::cutSelection()
{
    if (!m_timelineWidget->hasSelection()) return;

    // In a real implementation, this would cut the selected audio
    QMessageBox::information(this, "Cut Selection",
                            QString("Cut selection from %1 to %2")
                            .arg(formatTime(m_timelineWidget->selectionStart()))
                            .arg(formatTime(m_timelineWidget->selectionEnd())));

    setUnsavedChanges(true);
    m_timelineWidget->clearSelection();
}

void AudioEditor::deleteSelection()
{
    if (!m_timelineWidget->hasSelection()) return;

    // In a real implementation, this would delete the selected audio
    QMessageBox::information(this, "Delete Selection",
                            QString("Delete selection from %1 to %2")
                            .arg(formatTime(m_timelineWidget->selectionStart()))
                            .arg(formatTime(m_timelineWidget->selectionEnd())));

    setUnsavedChanges(true);
    m_timelineWidget->clearSelection();
}

void AudioEditor::splitAtPosition()
{
    qint64 position = m_mediaPlayer->position();

    if (position > 0 && position < m_mediaPlayer->duration()) {
        // Add split marker at current position
        m_timelineWidget->addSplitMarker(position);

        // Update status
        QMessageBox::information(this, "Split Audio",
                                QString("Added split marker at position %1\n\n"
                                       "Use Alt+Click to select segments between split markers.")
                                .arg(formatTime(position)));

        setUnsavedChanges(true);
    } else {
        QMessageBox::warning(this, "Invalid Position",
                           "Cannot split at the beginning or end of the audio.");
    }
}

void AudioEditor::saveFile()
{
    if (!m_hasUnsavedChanges) {
        statusBar()->showMessage("No changes to save");
        return;
    }

    // In a real implementation, this would save the edited audio
    QMessageBox::information(this, "Save File", "File saved successfully!");
    setUnsavedChanges(false);
    statusBar()->showMessage("File saved");
}

void AudioEditor::saveAsFile()
{
    QString fileName = QFileDialog::getSaveFileName(this,
                                                   "Save Audio As",
                                                   m_fileName,
                                                   "Audio Files (*.mp3 *.wav *.flac *.ogg)");
    if (!fileName.isEmpty()) {
        // In a real implementation, this would save the audio to the new file
        QMessageBox::information(this, "Save As", "File saved as: " + fileName);
        setUnsavedChanges(false);
        statusBar()->showMessage("File saved as: " + QFileInfo(fileName).fileName());
    }
}

void AudioEditor::exportFile()
{
    QString fileName = QFileDialog::getSaveFileName(this,
                                                   "Export Audio",
                                                   m_fileName,
                                                   "Audio Files (*.mp3 *.wav *.flac *.ogg)");
    if (!fileName.isEmpty()) {
        // In a real implementation, this would export the audio
        QMessageBox::information(this, "Export", "Audio exported to: " + fileName);
        statusBar()->showMessage("Audio exported");
    }
}

void AudioEditor::closeEditor()
{
    if (m_hasUnsavedChanges) {
        int ret = QMessageBox::question(this, "Unsaved Changes",
                                       "You have unsaved changes. Do you want to save before closing?",
                                       QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel);

        if (ret == QMessageBox::Save) {
            saveFile();
        } else if (ret == QMessageBox::Cancel) {
            return;
        }
    }

    close();
}

void AudioEditor::onPositionChanged(qint64 position)
{
    if (!m_positionSlider->isSliderDown()) {
        m_positionSlider->setValue(static_cast<int>(position));
    }

    m_timelineWidget->setPosition(position);
    updateTimeLabels();
}

void AudioEditor::onDurationChanged(qint64 duration)
{
    m_duration = duration;
    m_positionSlider->setRange(0, static_cast<int>(duration));
    m_timelineWidget->setDuration(duration);
    updateTimeLabels();
}

void AudioEditor::onPlaybackStateChanged()
{
    updatePlayPauseButton();
}

void AudioEditor::seekToPosition(int position)
{
    m_mediaPlayer->setPosition(position);
}

void AudioEditor::setVolume(int volume)
{
    float volumeLevel = volume / 100.0f;
    m_audioOutput->setVolume(volumeLevel);
    m_volumeLabel->setText(QString("%1%").arg(volume));
}

void AudioEditor::zoomIn()
{
    double currentZoom = m_timelineWidget->zoomLevel();
    m_timelineWidget->setZoomLevel(currentZoom * 1.5);
}

void AudioEditor::zoomOut()
{
    double currentZoom = m_timelineWidget->zoomLevel();
    m_timelineWidget->setZoomLevel(currentZoom / 1.5);
}

void AudioEditor::resetZoom()
{
    m_timelineWidget->setZoomLevel(1.0);
}

void AudioEditor::selectAll()
{
    m_timelineWidget->selectAll();
}

void AudioEditor::onSelectionChanged(qint64 start, qint64 end)
{
    bool hasSelection = (start != -1 && end != -1);

    m_cutButton->setEnabled(hasSelection);
    m_deleteButton->setEnabled(hasSelection);
    m_cutAction->setEnabled(hasSelection);
    m_deleteAction->setEnabled(hasSelection);

    if (hasSelection) {
        qint64 duration = qAbs(end - start);
        m_selectionLabel->setText(QString("Selection: %1 - %2 (Duration: %3)")
                                 .arg(formatTime(qMin(start, end)))
                                 .arg(formatTime(qMax(start, end)))
                                 .arg(formatTime(duration)));
    } else {
        m_selectionLabel->setText("No selection");
    }
}

void AudioEditor::onPositionClicked(qint64 position)
{
    // Seek to the clicked position
    m_mediaPlayer->setPosition(position);
}

void AudioEditor::onSegmentSelected(int segmentIndex)
{
    QPair<qint64, qint64> bounds = m_timelineWidget->getSegmentBounds(segmentIndex);
    if (bounds.first != -1 && bounds.second != -1) {
        qint64 duration = bounds.second - bounds.first;
        m_selectionLabel->setText(QString("Segment %1: %2 - %3 (Duration: %4)")
                                 .arg(segmentIndex + 1)
                                 .arg(formatTime(bounds.first))
                                 .arg(formatTime(bounds.second))
                                 .arg(formatTime(duration)));

        // Update status bar
        statusBar()->showMessage(QString("Selected segment %1 of %2")
                               .arg(segmentIndex + 1)
                               .arg(m_timelineWidget->splitMarkers().size() + 1), 3000);
    }
}

void AudioEditor::onSplitMarkerAdded(qint64 position)
{
    // Update status bar
    statusBar()->showMessage(QString("Split marker added at %1")
                           .arg(formatTime(position)), 3000);

    // Update window title to indicate unsaved changes
    setUnsavedChanges(true);
}

void AudioEditor::onSplitMarkerRemoved(qint64 position)
{
    // Update status bar
    statusBar()->showMessage(QString("Split marker removed from %1")
                           .arg(formatTime(position)), 3000);

    // Update window title to indicate unsaved changes
    setUnsavedChanges(true);
}

void AudioEditor::updatePlayPauseButton()
{
    if (m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState) {
        setButtonIcon(m_playPauseButton, "pause");
    } else {
        setButtonIcon(m_playPauseButton, "play");
    }
}

void AudioEditor::updateTimeLabels()
{
    m_currentTimeLabel->setText(formatTime(m_mediaPlayer->position()));
    m_totalTimeLabel->setText(formatTime(m_duration));
}

void AudioEditor::setButtonIcon(QPushButton *button, const QString &iconName)
{
    QString iconPath = QString(":/icons/icons/%1.svg").arg(iconName);
    QIcon icon(iconPath);
    button->setIcon(icon);
    button->setIconSize(QSize(20, 20));
    button->setText("");

    // Style the button
    button->setStyleSheet(
        "QPushButton {"
        "    background-color: #42a5f5;"
        "    border: none;"
        "    border-radius: 15px;"
        "    padding: 8px;"
        "    min-width: 30px;"
        "    min-height: 30px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #1976d2;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #0d47a1;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "}"
    );
}

QString AudioEditor::formatTime(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

bool AudioEditor::hasUnsavedChanges() const
{
    return m_hasUnsavedChanges;
}

void AudioEditor::setUnsavedChanges(bool hasChanges)
{
    m_hasUnsavedChanges = hasChanges;

    QString title = "Audio Editor - " + QFileInfo(m_filePath).fileName();
    if (hasChanges) {
        title += " *";
    }
    setWindowTitle(title);
}
