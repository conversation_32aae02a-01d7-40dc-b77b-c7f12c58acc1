#include "playlist.h"
#include <QFileInfo>
#include <QDir>
#include <QRegularExpression>

Playlist::Playlist(QObject *parent)
    : QObject(parent)
    , m_currentIndex(-1)
{
    m_supportedFormats << "mp3" << "wav" << "flac" << "ogg" << "m4a" << "aac";
}

Playlist::~Playlist()
{
}

void Playlist::addTrack(const QString &filePath)
{
    if (!filePath.isEmpty() && isValidAudioFile(filePath) && !m_tracks.contains(filePath)) {
        m_tracks.append(filePath);
        
        // If this is the first track, set it as current
        if (m_tracks.count() == 1) {
            m_currentIndex = 0;
            emit currentTrackChanged(filePath);
        }
        
        emit trackAdded(filePath);
        emit playlistChanged();
    }
}

void Playlist::addTracks(const QStringList &filePaths)
{
    bool hasNewTracks = false;
    bool firstTrack = m_tracks.isEmpty();
    
    for (const QString &filePath : filePaths) {
        if (!filePath.isEmpty() && isValidAudioFile(filePath) && !m_tracks.contains(filePath)) {
            m_tracks.append(filePath);
            emit trackAdded(filePath);
            hasNewTracks = true;
        }
    }
    
    if (hasNewTracks) {
        // If this was the first batch of tracks, set the first one as current
        if (firstTrack && !m_tracks.isEmpty()) {
            m_currentIndex = 0;
            emit currentTrackChanged(m_tracks.first());
        }
        
        emit playlistChanged();
    }
}

void Playlist::removeTrack(int index)
{
    if (index >= 0 && index < m_tracks.count()) {
        m_tracks.removeAt(index);
        
        // Adjust current index
        if (index < m_currentIndex) {
            m_currentIndex--;
        } else if (index == m_currentIndex) {
            // Current track was removed
            if (m_currentIndex >= m_tracks.count()) {
                m_currentIndex = m_tracks.count() - 1;
            }
            
            if (m_currentIndex >= 0) {
                emit currentTrackChanged(m_tracks.at(m_currentIndex));
            } else {
                emit currentTrackChanged(QString());
            }
        }
        
        emit trackRemoved(index);
        emit playlistChanged();
    }
}

void Playlist::removeTrack(const QString &filePath)
{
    int index = m_tracks.indexOf(filePath);
    if (index >= 0) {
        removeTrack(index);
    }
}

void Playlist::clear()
{
    m_tracks.clear();
    m_currentIndex = -1;
    emit tracksCleared();
    emit currentTrackChanged(QString());
    emit playlistChanged();
}

void Playlist::moveTrack(int from, int to)
{
    if (from >= 0 && from < m_tracks.count() && to >= 0 && to < m_tracks.count() && from != to) {
        m_tracks.move(from, to);
        
        // Adjust current index
        if (from == m_currentIndex) {
            m_currentIndex = to;
        } else if (from < m_currentIndex && to >= m_currentIndex) {
            m_currentIndex--;
        } else if (from > m_currentIndex && to <= m_currentIndex) {
            m_currentIndex++;
        }
        
        emit playlistChanged();
    }
}

QString Playlist::currentTrack() const
{
    if (m_currentIndex >= 0 && m_currentIndex < m_tracks.count()) {
        return m_tracks.at(m_currentIndex);
    }
    return QString();
}

QString Playlist::nextTrack()
{
    if (m_tracks.isEmpty()) {
        return QString();
    }
    
    int nextIndex = (m_currentIndex + 1) % m_tracks.count();
    m_currentIndex = nextIndex;
    
    QString track = m_tracks.at(m_currentIndex);
    emit currentTrackChanged(track);
    return track;
}

QString Playlist::previousTrack()
{
    if (m_tracks.isEmpty()) {
        return QString();
    }
    
    int prevIndex = m_currentIndex - 1;
    if (prevIndex < 0) {
        prevIndex = m_tracks.count() - 1;
    }
    m_currentIndex = prevIndex;
    
    QString track = m_tracks.at(m_currentIndex);
    emit currentTrackChanged(track);
    return track;
}

void Playlist::setCurrentIndex(int index)
{
    if (index >= 0 && index < m_tracks.count() && index != m_currentIndex) {
        m_currentIndex = index;
        emit currentTrackChanged(m_tracks.at(m_currentIndex));
    }
}

int Playlist::currentIndex() const
{
    return m_currentIndex;
}

int Playlist::count() const
{
    return m_tracks.count();
}

QStringList Playlist::tracks() const
{
    return m_tracks;
}

QString Playlist::trackAt(int index) const
{
    if (index >= 0 && index < m_tracks.count()) {
        return m_tracks.at(index);
    }
    return QString();
}

bool Playlist::isEmpty() const
{
    return m_tracks.isEmpty();
}

bool Playlist::contains(const QString &filePath) const
{
    return m_tracks.contains(filePath);
}

int Playlist::indexOf(const QString &filePath) const
{
    return m_tracks.indexOf(filePath);
}

QString Playlist::getTrackTitle(const QString &filePath) const
{
    return extractTitleFromFileName(filePath);
}

QString Playlist::getTrackArtist(const QString &filePath) const
{
    // For now, return empty - would be extracted from metadata in full implementation
    Q_UNUSED(filePath)
    return QString();
}

qint64 Playlist::getTrackDuration(const QString &filePath) const
{
    // For now, return 0 - would be extracted from metadata in full implementation
    Q_UNUSED(filePath)
    return 0;
}

QString Playlist::formatDuration(qint64 milliseconds) const
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;
    
    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

bool Playlist::isValidAudioFile(const QString &filePath) const
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        return false;
    }
    
    QString suffix = fileInfo.suffix().toLower();
    return m_supportedFormats.contains(suffix);
}

QString Playlist::extractTitleFromFileName(const QString &filePath) const
{
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName();
    
    // Remove common patterns like "01 - " or "01. "
    QRegularExpression trackNumberPattern("^\\d+[\\s\\-\\.]+");
    baseName.remove(trackNumberPattern);
    
    // Replace underscores with spaces
    baseName.replace('_', ' ');
    
    return baseName.isEmpty() ? fileInfo.baseName() : baseName;
}
