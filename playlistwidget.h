#ifndef PLAYLISTWIDGET_H
#define PLAYLISTWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QListWidget>
#include <QListWidgetItem>
#include <QPushButton>
#include <QLabel>
#include <QMenu>
#include <QAction>
#include <QContextMenuEvent>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>
#include <QFileDialog>
#include <QDir>

class Playlist;

class PlaylistWidget : public QWidget
{
    Q_OBJECT

public:
    explicit PlaylistWidget(QWidget *parent = nullptr);
    ~PlaylistWidget();

    // File management
    void addFile(const QString &filePath);
    void addFiles(const QStringList &filePaths);
    void addFolder(const QString &folderPath);
    
    // Playlist control
    void playNext();
    void playPrevious();
    void setCurrentTrack(const QString &filePath);

signals:
    void trackSelected(const QString &filePath);
    void playTrack(const QString &filePath);

protected:
    void contextMenuEvent(QContextMenuEvent *event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private slots:
    void onItemDoubleClicked(QListWidgetItem *item);
    void onItemSelectionChanged();
    void onTrackAdded(const QString &filePath);
    void onTrackRemoved(int index);
    void onTracksCleared();
    void onCurrentTrackChanged(const QString &filePath);
    void onPlaylistChanged();
    
    // Context menu actions
    void playSelectedTrack();
    void removeSelectedTrack();
    void clearPlaylist();
    void moveTrackUp();
    void moveTrackDown();

private:
    void setupUI();
    void setupContextMenu();
    void updateTrackCount();
    void updateCurrentTrackDisplay();
    QStringList getAudioFilesFromFolder(const QString &folderPath);
    QString formatTrackInfo(const QString &filePath);

    // UI Components
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_headerLayout;
    QLabel *m_titleLabel;
    QLabel *m_countLabel;
    QListWidget *m_listWidget;
    
    // Context menu
    QMenu *m_contextMenu;
    QAction *m_playAction;
    QAction *m_removeAction;
    QAction *m_clearAction;
    QAction *m_moveUpAction;
    QAction *m_moveDownAction;
    
    // Core components
    Playlist *m_playlist;
    
    // State
    QString m_currentTrackPath;
};

#endif // PLAYLISTWIDGET_H
