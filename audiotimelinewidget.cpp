#include "audiotimelinewidget.h"
#include <QPaintEvent>
#include <QApplication>
#include <QtMath>

AudioTimelineWidget::AudioTimelineWidget(QWidget *parent)
    : QWidget(parent)
    , m_duration(0)
    , m_position(0)
    , m_zoomLevel(1.0)
    , m_waveformHeight(120)
    , m_timelineHeight(30)
    , m_markerHeight(20)
    , m_selectionStart(-1)
    , m_selectionEnd(-1)
    , m_isSelecting(false)
    , m_selectionStartPixel(0)
    , m_isDragging(false)
{
    setMinimumHeight(m_waveformHeight + m_timelineHeight + m_markerHeight);
    setMouseTracking(true);
    
    // Set up colors for dark theme
    m_backgroundColor = QColor(26, 26, 26);
    m_waveformColor = QColor(66, 165, 245);
    m_playheadColor = QColor(255, 193, 7);
    m_selectionColor = QColor(66, 165, 245, 80);
    m_timelineColor = QColor(85, 85, 85);
    m_textColor = QColor(204, 204, 204);
    
    generateDummyWaveform();
}

void AudioTimelineWidget::setDuration(qint64 duration)
{
    m_duration = duration;
    generateDummyWaveform();
    update();
}

void AudioTimelineWidget::setPosition(qint64 position)
{
    m_position = position;
    update();
}

void AudioTimelineWidget::setZoomLevel(double zoom)
{
    m_zoomLevel = qMax(0.1, qMin(10.0, zoom));
    generateDummyWaveform();
    update();
}

void AudioTimelineWidget::setSelection(qint64 start, qint64 end)
{
    m_selectionStart = start;
    m_selectionEnd = end;
    update();
    emit selectionChanged(start, end);
}

void AudioTimelineWidget::clearSelection()
{
    m_selectionStart = -1;
    m_selectionEnd = -1;
    update();
    emit selectionChanged(-1, -1);
}

void AudioTimelineWidget::selectAll()
{
    if (m_duration > 0) {
        setSelection(0, m_duration);
    }
}

QSize AudioTimelineWidget::sizeHint() const
{
    int width = static_cast<int>(m_duration * m_zoomLevel / 1000.0 * 0.1); // Rough estimate
    return QSize(qMax(800, width), m_waveformHeight + m_timelineHeight + m_markerHeight);
}

QSize AudioTimelineWidget::minimumSizeHint() const
{
    return QSize(400, m_waveformHeight + m_timelineHeight + m_markerHeight);
}

void AudioTimelineWidget::paintEvent(QPaintEvent *event)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Fill background
    painter.fillRect(rect(), m_backgroundColor);
    
    if (m_duration <= 0) {
        painter.setPen(m_textColor);
        painter.drawText(rect(), Qt::AlignCenter, "No audio loaded");
        return;
    }
    
    drawWaveform(painter);
    drawSelection(painter);
    drawPlayhead(painter);
    drawTimeline(painter);
    drawTimeMarkers(painter);
}

void AudioTimelineWidget::drawWaveform(QPainter &painter)
{
    if (m_waveformData.isEmpty()) return;
    
    painter.setPen(QPen(m_waveformColor, 1));
    painter.setBrush(QBrush(m_waveformColor.darker(150)));
    
    int waveformTop = m_markerHeight;
    int waveformBottom = waveformTop + m_waveformHeight;
    int centerY = waveformTop + m_waveformHeight / 2;
    
    int samplesPerPixel = qMax(1, m_waveformData.size() / width());
    
    for (int x = 0; x < width(); ++x) {
        int sampleIndex = x * samplesPerPixel;
        if (sampleIndex >= m_waveformData.size()) break;
        
        float amplitude = m_waveformData[sampleIndex];
        int waveHeight = static_cast<int>(amplitude * m_waveformHeight / 2);
        
        painter.drawLine(x, centerY - waveHeight, x, centerY + waveHeight);
    }
}

void AudioTimelineWidget::drawTimeline(QPainter &painter)
{
    int timelineTop = m_markerHeight + m_waveformHeight;
    int timelineBottom = timelineTop + m_timelineHeight;
    
    painter.fillRect(0, timelineTop, width(), m_timelineHeight, m_timelineColor);
    
    painter.setPen(QPen(m_textColor, 1));
    painter.drawLine(0, timelineTop, width(), timelineTop);
}

void AudioTimelineWidget::drawPlayhead(QPainter &painter)
{
    if (m_duration <= 0) return;
    
    int playheadX = timeToPixel(m_position);
    
    painter.setPen(QPen(m_playheadColor, 2));
    painter.drawLine(playheadX, 0, playheadX, height());
    
    // Draw playhead triangle at top
    QPolygon triangle;
    triangle << QPoint(playheadX - 5, 0)
             << QPoint(playheadX + 5, 0)
             << QPoint(playheadX, 10);
    
    painter.setBrush(m_playheadColor);
    painter.drawPolygon(triangle);
}

void AudioTimelineWidget::drawSelection(QPainter &painter)
{
    if (!hasSelection()) return;
    
    int startX = timeToPixel(m_selectionStart);
    int endX = timeToPixel(m_selectionEnd);
    
    if (startX > endX) qSwap(startX, endX);
    
    painter.fillRect(startX, 0, endX - startX, height(), m_selectionColor);
    
    // Draw selection borders
    painter.setPen(QPen(m_selectionColor.darker(150), 2));
    painter.drawLine(startX, 0, startX, height());
    painter.drawLine(endX, 0, endX, height());
}

void AudioTimelineWidget::drawTimeMarkers(QPainter &painter)
{
    if (m_duration <= 0) return;
    
    painter.setPen(m_textColor);
    QFont font = painter.font();
    font.setPointSize(8);
    painter.setFont(font);
    
    // Calculate marker interval based on zoom level
    qint64 markerInterval = 10000; // 10 seconds
    if (m_zoomLevel > 2.0) markerInterval = 5000;  // 5 seconds
    if (m_zoomLevel > 5.0) markerInterval = 1000;  // 1 second
    
    for (qint64 time = 0; time <= m_duration; time += markerInterval) {
        int x = timeToPixel(time);
        if (x < 0 || x > width()) continue;
        
        painter.drawLine(x, 0, x, m_markerHeight);
        
        QString timeText = formatTime(time);
        QRect textRect(x - 30, 0, 60, m_markerHeight);
        painter.drawText(textRect, Qt::AlignCenter, timeText);
    }
}

void AudioTimelineWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        qint64 clickTime = pixelToTime(event->x());
        
        if (event->modifiers() & Qt::ShiftModifier && hasSelection()) {
            // Extend selection
            if (qAbs(clickTime - m_selectionStart) < qAbs(clickTime - m_selectionEnd)) {
                setSelection(clickTime, m_selectionEnd);
            } else {
                setSelection(m_selectionStart, clickTime);
            }
        } else if (event->modifiers() & Qt::ControlModifier) {
            // Start new selection
            m_isSelecting = true;
            m_selectionStartPixel = event->x();
            setSelection(clickTime, clickTime);
        } else {
            // Clear selection and set position
            clearSelection();
            emit positionClicked(clickTime);
        }
    }
}

void AudioTimelineWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_isSelecting) {
        qint64 startTime = pixelToTime(m_selectionStartPixel);
        qint64 endTime = pixelToTime(event->x());
        setSelection(startTime, endTime);
    }
}

void AudioTimelineWidget::mouseReleaseEvent(QMouseEvent *event)
{
    Q_UNUSED(event)
    m_isSelecting = false;
}

void AudioTimelineWidget::wheelEvent(QWheelEvent *event)
{
    if (event->modifiers() & Qt::ControlModifier) {
        // Zoom with Ctrl+Wheel
        double zoomFactor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
        setZoomLevel(m_zoomLevel * zoomFactor);
        event->accept();
    } else {
        QWidget::wheelEvent(event);
    }
}

void AudioTimelineWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    update();
}

qint64 AudioTimelineWidget::pixelToTime(int pixel) const
{
    if (width() <= 0 || m_duration <= 0) return 0;
    return static_cast<qint64>(pixel * m_duration / (width() * m_zoomLevel));
}

int AudioTimelineWidget::timeToPixel(qint64 time) const
{
    if (m_duration <= 0) return 0;
    return static_cast<int>(time * width() * m_zoomLevel / m_duration);
}

void AudioTimelineWidget::generateDummyWaveform()
{
    // Generate a dummy waveform for visualization
    // In a real implementation, this would analyze the actual audio file
    m_waveformData.clear();
    
    if (m_duration <= 0) return;
    
    int samples = static_cast<int>(width() * m_zoomLevel);
    m_waveformData.reserve(samples);
    
    for (int i = 0; i < samples; ++i) {
        // Generate a simple sine wave with some randomness
        double t = static_cast<double>(i) / samples;
        double amplitude = 0.5 * qSin(t * 20 * M_PI) + 0.3 * qSin(t * 100 * M_PI) + 0.2 * (qrand() / double(RAND_MAX) - 0.5);
        m_waveformData.append(static_cast<float>(qBound(-1.0, amplitude, 1.0)));
    }
}

QString AudioTimelineWidget::formatTime(qint64 milliseconds) const
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}
