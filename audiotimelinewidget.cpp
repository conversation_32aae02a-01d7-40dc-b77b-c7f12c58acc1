#include "audiotimelinewidget.h"
#include <QPaintEvent>
#include <QApplication>
#include <QtMath>
#include <QRandomGenerator>
#include <QMenu>
#include <QAction>

AudioTimelineWidget::AudioTimelineWidget(QWidget *parent)
    : QWidget(parent)
    , m_duration(0)
    , m_position(0)
    , m_zoomLevel(1.0)
    , m_waveformHeight(120)
    , m_timelineHeight(30)
    , m_markerHeight(20)
    , m_selectionStart(-1)
    , m_selectionEnd(-1)
    , m_isSelecting(false)
    , m_selectionStartPixel(0)
    , m_selectedSegment(-1)
    , m_isDragging(false)
{
    setMinimumHeight(m_waveformHeight + m_timelineHeight + m_markerHeight);
    setMouseTracking(true);
    
    // Set up colors for dark theme
    m_backgroundColor = QColor(26, 26, 26);
    m_waveformColor = QColor(66, 165, 245);
    m_playheadColor = QColor(255, 193, 7);
    m_selectionColor = QColor(66, 165, 245, 80);
    m_timelineColor = QColor(85, 85, 85);
    m_textColor = QColor(204, 204, 204);
    m_splitMarkerColor = QColor(255, 87, 34);  // Orange for split markers
    m_segmentHighlightColor = QColor(76, 175, 80, 60);  // Green highlight for selected segment
    
    generateDummyWaveform();
}

void AudioTimelineWidget::setDuration(qint64 duration)
{
    m_duration = duration;
    generateDummyWaveform();
    update();
}

void AudioTimelineWidget::setPosition(qint64 position)
{
    m_position = position;
    update();
}

void AudioTimelineWidget::setZoomLevel(double zoom)
{
    m_zoomLevel = qMax(0.1, qMin(10.0, zoom));
    generateDummyWaveform();
    update();
}

void AudioTimelineWidget::setSelection(qint64 start, qint64 end)
{
    m_selectionStart = start;
    m_selectionEnd = end;
    update();
    emit selectionChanged(start, end);
}

void AudioTimelineWidget::clearSelection()
{
    m_selectionStart = -1;
    m_selectionEnd = -1;
    update();
    emit selectionChanged(-1, -1);
}

void AudioTimelineWidget::selectAll()
{
    if (m_duration > 0) {
        setSelection(0, m_duration);
    }
}

void AudioTimelineWidget::addSplitMarker(qint64 position)
{
    if (position >= 0 && position <= m_duration && !m_splitMarkers.contains(position)) {
        m_splitMarkers.append(position);
        std::sort(m_splitMarkers.begin(), m_splitMarkers.end());
        update();
        emit splitMarkerAdded(position);
    }
}

void AudioTimelineWidget::removeSplitMarker(qint64 position)
{
    int index = m_splitMarkers.indexOf(position);
    if (index != -1) {
        m_splitMarkers.removeAt(index);
        update();
        emit splitMarkerRemoved(position);
    }
}

void AudioTimelineWidget::clearSplitMarkers()
{
    m_splitMarkers.clear();
    m_selectedSegment = -1;
    update();
}

void AudioTimelineWidget::selectSegment(int segmentIndex)
{
    QPair<qint64, qint64> bounds = getSegmentBounds(segmentIndex);
    if (bounds.first != -1 && bounds.second != -1) {
        m_selectedSegment = segmentIndex;
        setSelection(bounds.first, bounds.second);
        update();
        emit segmentSelected(segmentIndex);
    }
}

int AudioTimelineWidget::getSegmentAt(qint64 position) const
{
    if (m_splitMarkers.isEmpty()) {
        return 0; // Only one segment
    }

    for (int i = 0; i < m_splitMarkers.size(); ++i) {
        if (position < m_splitMarkers[i]) {
            return i;
        }
    }

    return m_splitMarkers.size(); // Last segment
}

QPair<qint64, qint64> AudioTimelineWidget::getSegmentBounds(int segmentIndex) const
{
    if (segmentIndex < 0) {
        return QPair<qint64, qint64>(-1, -1);
    }

    qint64 start = 0;
    qint64 end = m_duration;

    if (segmentIndex > 0 && segmentIndex - 1 < m_splitMarkers.size()) {
        start = m_splitMarkers[segmentIndex - 1];
    }

    if (segmentIndex < m_splitMarkers.size()) {
        end = m_splitMarkers[segmentIndex];
    }

    if (segmentIndex > m_splitMarkers.size()) {
        return QPair<qint64, qint64>(-1, -1);
    }

    return QPair<qint64, qint64>(start, end);
}

QSize AudioTimelineWidget::sizeHint() const
{
    int width = static_cast<int>(m_duration * m_zoomLevel / 1000.0 * 0.1); // Rough estimate
    return QSize(qMax(800, width), m_waveformHeight + m_timelineHeight + m_markerHeight);
}

QSize AudioTimelineWidget::minimumSizeHint() const
{
    return QSize(400, m_waveformHeight + m_timelineHeight + m_markerHeight);
}

void AudioTimelineWidget::paintEvent(QPaintEvent *)
{
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Fill background
    painter.fillRect(rect(), m_backgroundColor);
    
    if (m_duration <= 0) {
        painter.setPen(m_textColor);
        painter.drawText(rect(), Qt::AlignCenter, "No audio loaded");
        return;
    }
    
    drawSegments(painter);
    drawWaveform(painter);
    drawSelection(painter);
    drawSplitMarkers(painter);
    drawPlayhead(painter);
    drawTimeline(painter);
    drawTimeMarkers(painter);
}

void AudioTimelineWidget::drawWaveform(QPainter &painter)
{
    if (m_waveformData.isEmpty()) return;
    
    painter.setPen(QPen(m_waveformColor, 1));
    painter.setBrush(QBrush(m_waveformColor.darker(150)));
    
    int waveformTop = m_markerHeight;
    int centerY = waveformTop + m_waveformHeight / 2;
    
    int samplesPerPixel = qMax(1, m_waveformData.size() / width());
    
    for (int x = 0; x < width(); ++x) {
        int sampleIndex = x * samplesPerPixel;
        if (sampleIndex >= m_waveformData.size()) break;
        
        float amplitude = m_waveformData[sampleIndex];
        int waveHeight = static_cast<int>(amplitude * m_waveformHeight / 2);
        
        painter.drawLine(x, centerY - waveHeight, x, centerY + waveHeight);
    }
}

void AudioTimelineWidget::drawTimeline(QPainter &painter)
{
    int timelineTop = m_markerHeight + m_waveformHeight;

    painter.fillRect(0, timelineTop, width(), m_timelineHeight, m_timelineColor);

    painter.setPen(QPen(m_textColor, 1));
    painter.drawLine(0, timelineTop, width(), timelineTop);
}

void AudioTimelineWidget::drawPlayhead(QPainter &painter)
{
    if (m_duration <= 0) return;
    
    int playheadX = timeToPixel(m_position);
    
    painter.setPen(QPen(m_playheadColor, 2));
    painter.drawLine(playheadX, 0, playheadX, height());
    
    // Draw playhead triangle at top
    QPolygon triangle;
    triangle << QPoint(playheadX - 5, 0)
             << QPoint(playheadX + 5, 0)
             << QPoint(playheadX, 10);
    
    painter.setBrush(m_playheadColor);
    painter.drawPolygon(triangle);
}

void AudioTimelineWidget::drawSelection(QPainter &painter)
{
    if (!hasSelection()) return;
    
    int startX = timeToPixel(m_selectionStart);
    int endX = timeToPixel(m_selectionEnd);
    
    if (startX > endX) qSwap(startX, endX);
    
    painter.fillRect(startX, 0, endX - startX, height(), m_selectionColor);
    
    // Draw selection borders
    painter.setPen(QPen(m_selectionColor.darker(150), 2));
    painter.drawLine(startX, 0, startX, height());
    painter.drawLine(endX, 0, endX, height());
}

void AudioTimelineWidget::drawTimeMarkers(QPainter &painter)
{
    if (m_duration <= 0) return;
    
    painter.setPen(m_textColor);
    QFont font = painter.font();
    font.setPointSize(8);
    painter.setFont(font);
    
    // Calculate marker interval based on zoom level
    qint64 markerInterval = 10000; // 10 seconds
    if (m_zoomLevel > 2.0) markerInterval = 5000;  // 5 seconds
    if (m_zoomLevel > 5.0) markerInterval = 1000;  // 1 second
    
    for (qint64 time = 0; time <= m_duration; time += markerInterval) {
        int x = timeToPixel(time);
        if (x < 0 || x > width()) continue;
        
        painter.drawLine(x, 0, x, m_markerHeight);
        
        QString timeText = formatTime(time);
        QRect textRect(x - 30, 0, 60, m_markerHeight);
        painter.drawText(textRect, Qt::AlignCenter, timeText);
    }
}

void AudioTimelineWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() == Qt::LeftButton) {
        qint64 clickTime = pixelToTime(static_cast<int>(event->position().x()));
        
        if (event->modifiers() & Qt::ShiftModifier && hasSelection()) {
            // Extend selection
            if (qAbs(clickTime - m_selectionStart) < qAbs(clickTime - m_selectionEnd)) {
                setSelection(clickTime, m_selectionEnd);
            } else {
                setSelection(m_selectionStart, clickTime);
            }
        } else if (event->modifiers() & Qt::ControlModifier) {
            // Start new selection
            m_isSelecting = true;
            m_selectionStartPixel = static_cast<int>(event->position().x());
            setSelection(clickTime, clickTime);
        } else if (event->modifiers() & Qt::AltModifier) {
            // Select segment at click position
            int segmentIndex = getSegmentAt(clickTime);
            selectSegment(segmentIndex);
        } else {
            // Clear selection and set position
            clearSelection();
            emit positionClicked(clickTime);
        }
    }
}

void AudioTimelineWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_isSelecting) {
        qint64 startTime = pixelToTime(m_selectionStartPixel);
        qint64 endTime = pixelToTime(static_cast<int>(event->position().x()));
        setSelection(startTime, endTime);
    }
}

void AudioTimelineWidget::mouseReleaseEvent(QMouseEvent *event)
{
    Q_UNUSED(event)
    m_isSelecting = false;
}

void AudioTimelineWidget::wheelEvent(QWheelEvent *event)
{
    if (event->modifiers() & Qt::ControlModifier) {
        // Zoom with Ctrl+Wheel
        double zoomFactor = event->angleDelta().y() > 0 ? 1.2 : 0.8;
        setZoomLevel(m_zoomLevel * zoomFactor);
        event->accept();
    } else {
        QWidget::wheelEvent(event);
    }
}

void AudioTimelineWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    update();
}

qint64 AudioTimelineWidget::pixelToTime(int pixel) const
{
    if (width() <= 0 || m_duration <= 0) return 0;
    return static_cast<qint64>(pixel * m_duration / (width() * m_zoomLevel));
}

int AudioTimelineWidget::timeToPixel(qint64 time) const
{
    if (m_duration <= 0) return 0;
    return static_cast<int>(time * width() * m_zoomLevel / m_duration);
}

void AudioTimelineWidget::generateDummyWaveform()
{
    // Generate a dummy waveform for visualization
    // In a real implementation, this would analyze the actual audio file
    m_waveformData.clear();
    
    if (m_duration <= 0) return;
    
    int samples = static_cast<int>(width() * m_zoomLevel);
    m_waveformData.reserve(samples);
    
    for (int i = 0; i < samples; ++i) {
        // Generate a simple sine wave with some randomness
        double t = static_cast<double>(i) / samples;
        double amplitude = 0.5 * qSin(t * 20 * M_PI) + 0.3 * qSin(t * 100 * M_PI) + 0.2 * (QRandomGenerator::global()->generateDouble() - 0.5);
        m_waveformData.append(static_cast<float>(qBound(-1.0, amplitude, 1.0)));
    }
}

QString AudioTimelineWidget::formatTime(qint64 milliseconds) const
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

void AudioTimelineWidget::drawSplitMarkers(QPainter &painter)
{
    painter.setPen(QPen(m_splitMarkerColor, 2));

    for (qint64 markerTime : m_splitMarkers) {
        int x = timeToPixel(markerTime);
        if (x >= 0 && x <= width()) {
            // Draw vertical line for split marker
            painter.drawLine(x, 0, x, height());

            // Draw small triangle at the top
            QPolygon triangle;
            triangle << QPoint(x - 4, 0) << QPoint(x + 4, 0) << QPoint(x, 8);
            painter.fillPolygon(triangle, m_splitMarkerColor);

            // Draw small triangle at the bottom
            QPolygon bottomTriangle;
            bottomTriangle << QPoint(x - 4, height()) << QPoint(x + 4, height()) << QPoint(x, height() - 8);
            painter.fillPolygon(bottomTriangle, m_splitMarkerColor);
        }
    }
}

void AudioTimelineWidget::drawSegments(QPainter &painter)
{
    if (m_selectedSegment >= 0) {
        QPair<qint64, qint64> bounds = getSegmentBounds(m_selectedSegment);
        if (bounds.first != -1 && bounds.second != -1) {
            int startX = timeToPixel(bounds.first);
            int endX = timeToPixel(bounds.second);

            // Highlight the selected segment
            QRect segmentRect(startX, 0, endX - startX, height());
            painter.fillRect(segmentRect, m_segmentHighlightColor);

            // Draw border around selected segment
            painter.setPen(QPen(QColor(76, 175, 80), 2));
            painter.drawRect(segmentRect);
        }
    }
}

void AudioTimelineWidget::contextMenuEvent(QContextMenuEvent *event)
{
    qint64 clickTime = pixelToTime(event->x());

    QMenu contextMenu(this);

    // Check if there's a split marker near the click position (within 5 pixels)
    qint64 tolerance = pixelToTime(5) - pixelToTime(0);
    qint64 nearestMarker = -1;

    for (qint64 marker : m_splitMarkers) {
        if (qAbs(marker - clickTime) <= tolerance) {
            nearestMarker = marker;
            break;
        }
    }

    if (nearestMarker != -1) {
        // Context menu for existing split marker
        QAction *removeAction = contextMenu.addAction("Remove Split Marker");
        connect(removeAction, &QAction::triggered, [this, nearestMarker]() {
            removeSplitMarker(nearestMarker);
        });
    } else {
        // Context menu for adding split marker
        QAction *addAction = contextMenu.addAction("Add Split Marker Here");
        connect(addAction, &QAction::triggered, [this, clickTime]() {
            addSplitMarker(clickTime);
        });
    }

    contextMenu.addSeparator();

    // Segment selection options
    int segmentIndex = getSegmentAt(clickTime);
    QAction *selectSegmentAction = contextMenu.addAction(QString("Select Segment %1").arg(segmentIndex + 1));
    connect(selectSegmentAction, &QAction::triggered, [this, segmentIndex]() {
        selectSegment(segmentIndex);
    });

    if (!m_splitMarkers.isEmpty()) {
        QAction *clearAllAction = contextMenu.addAction("Clear All Split Markers");
        connect(clearAllAction, &QAction::triggered, [this]() {
            clearSplitMarkers();
        });
    }

    contextMenu.exec(event->globalPos());
}
