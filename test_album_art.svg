<?xml version="1.0" encoding="UTF-8"?>
<svg width="300" height="300" viewBox="0 0 300 300" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="shine" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
    </radialGradient>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="300" fill="url(#bg)"/>
  
  <!-- Music waves -->
  <g opacity="0.3">
    <path d="M50 150 Q100 100 150 150 T250 150" stroke="white" stroke-width="3" fill="none"/>
    <path d="M50 170 Q100 120 150 170 T250 170" stroke="white" stroke-width="2" fill="none"/>
    <path d="M50 190 Q100 140 150 190 T250 190" stroke="white" stroke-width="2" fill="none"/>
  </g>
  
  <!-- Central music note -->
  <g transform="translate(125, 100)">
    <circle cx="25" cy="75" r="40" fill="white" opacity="0.9"/>
    <path d="M25 35V95C22 93 18 92 15 92C9 92 5 96 5 101S9 110 15 110 25 106 25 101V45H35V40H25V35Z" fill="#333"/>
  </g>
  
  <!-- Shine overlay -->
  <rect width="300" height="300" fill="url(#shine)"/>
  
  <!-- Border -->
  <rect x="2" y="2" width="296" height="296" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
</svg>
