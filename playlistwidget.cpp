#include "playlistwidget.h"
#include "playlist.h"
#include <QFileInfo>
#include <QDirIterator>
#include <QMessageBox>
#include <QApplication>
#include <QStandardPaths>

PlaylistWidget::PlaylistWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_headerLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_countLabel(nullptr)
    , m_listWidget(nullptr)
    , m_contextMenu(nullptr)
    , m_playlist(nullptr)
{
    setMinimumWidth(300);
    setAcceptDrops(true);
    
    m_playlist = new Playlist(this);
    
    setupUI();
    setupContextMenu();
    
    // Connect playlist signals
    connect(m_playlist, &Playlist::trackAdded, this, &PlaylistWidget::onTrackAdded);
    connect(m_playlist, &Playlist::trackRemoved, this, &PlaylistWidget::onTrackRemoved);
    connect(m_playlist, &Playlist::tracksCleared, this, &PlaylistWidget::onTracksCleared);
    connect(m_playlist, &Playlist::currentTrackChanged, this, &PlaylistWidget::onCurrentTrackChanged);
    connect(m_playlist, &Playlist::playlistChanged, this, &PlaylistWidget::onPlaylistChanged);
    
    // Connect list widget signals
    connect(m_listWidget, &QListWidget::itemDoubleClicked, this, &PlaylistWidget::onItemDoubleClicked);
    connect(m_listWidget, &QListWidget::itemSelectionChanged, this, &PlaylistWidget::onItemSelectionChanged);
}

PlaylistWidget::~PlaylistWidget()
{
}

void PlaylistWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    m_mainLayout->setSpacing(10);
    
    // Header
    m_headerLayout = new QHBoxLayout;
    m_titleLabel = new QLabel("Playlist");
    m_titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; color: white;");
    
    m_countLabel = new QLabel("0 tracks");
    m_countLabel->setStyleSheet("color: #cccccc;");
    
    m_headerLayout->addWidget(m_titleLabel);
    m_headerLayout->addStretch();
    m_headerLayout->addWidget(m_countLabel);
    
    m_mainLayout->addLayout(m_headerLayout);
    
    // List widget
    m_listWidget = new QListWidget;
    m_listWidget->setStyleSheet(
        "QListWidget {"
        "    background-color: #2b2b2b;"
        "    border: 1px solid #555555;"
        "    border-radius: 5px;"
        "    color: white;"
        "    selection-background-color: #42a5f5;"
        "}"
        "QListWidget::item {"
        "    padding: 8px;"
        "    border-bottom: 1px solid #404040;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: #404040;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #42a5f5;"
        "    color: white;"
        "}"
    );
    
    m_listWidget->setAlternatingRowColors(false);
    m_listWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    
    m_mainLayout->addWidget(m_listWidget);
}

void PlaylistWidget::setupContextMenu()
{
    m_contextMenu = new QMenu(this);
    
    m_playAction = new QAction("Play", this);
    m_playAction->setShortcut(QKeySequence(Qt::Key_Return));
    connect(m_playAction, &QAction::triggered, this, &PlaylistWidget::playSelectedTrack);
    m_contextMenu->addAction(m_playAction);
    
    m_contextMenu->addSeparator();
    
    m_moveUpAction = new QAction("Move Up", this);
    m_moveUpAction->setShortcut(QKeySequence("Ctrl+Up"));
    connect(m_moveUpAction, &QAction::triggered, this, &PlaylistWidget::moveTrackUp);
    m_contextMenu->addAction(m_moveUpAction);
    
    m_moveDownAction = new QAction("Move Down", this);
    m_moveDownAction->setShortcut(QKeySequence("Ctrl+Down"));
    connect(m_moveDownAction, &QAction::triggered, this, &PlaylistWidget::moveTrackDown);
    m_contextMenu->addAction(m_moveDownAction);
    
    m_contextMenu->addSeparator();
    
    m_removeAction = new QAction("Remove", this);
    m_removeAction->setShortcut(QKeySequence::Delete);
    connect(m_removeAction, &QAction::triggered, this, &PlaylistWidget::removeSelectedTrack);
    m_contextMenu->addAction(m_removeAction);
    
    m_clearAction = new QAction("Clear Playlist", this);
    connect(m_clearAction, &QAction::triggered, this, &PlaylistWidget::clearPlaylist);
    m_contextMenu->addAction(m_clearAction);
}

void PlaylistWidget::addFile(const QString &filePath)
{
    m_playlist->addTrack(filePath);
}

void PlaylistWidget::addFiles(const QStringList &filePaths)
{
    m_playlist->addTracks(filePaths);
}

void PlaylistWidget::addFolder(const QString &folderPath)
{
    QStringList audioFiles = getAudioFilesFromFolder(folderPath);
    if (!audioFiles.isEmpty()) {
        addFiles(audioFiles);
    }
}

void PlaylistWidget::playNext()
{
    QString nextTrack = m_playlist->nextTrack();
    if (!nextTrack.isEmpty()) {
        emit playTrack(nextTrack);
    }
}

void PlaylistWidget::playPrevious()
{
    QString prevTrack = m_playlist->previousTrack();
    if (!prevTrack.isEmpty()) {
        emit playTrack(prevTrack);
    }
}

void PlaylistWidget::setCurrentTrack(const QString &filePath)
{
    int index = m_playlist->indexOf(filePath);
    if (index >= 0) {
        m_playlist->setCurrentIndex(index);
    }
}

void PlaylistWidget::contextMenuEvent(QContextMenuEvent *event)
{
    if (m_listWidget->itemAt(m_listWidget->mapFromParent(event->pos()))) {
        // Update action states
        int currentRow = m_listWidget->currentRow();
        m_moveUpAction->setEnabled(currentRow > 0);
        m_moveDownAction->setEnabled(currentRow >= 0 && currentRow < m_listWidget->count() - 1);
        m_playAction->setEnabled(currentRow >= 0);
        m_removeAction->setEnabled(currentRow >= 0);
        
        m_contextMenu->exec(event->globalPos());
    }
}

void PlaylistWidget::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        event->acceptProposedAction();
    }
}

void PlaylistWidget::dropEvent(QDropEvent *event)
{
    QStringList audioFiles;
    for (const QUrl &url : event->mimeData()->urls()) {
        if (url.isLocalFile()) {
            QString filePath = url.toLocalFile();
            QFileInfo fileInfo(filePath);
            
            if (fileInfo.isDir()) {
                audioFiles.append(getAudioFilesFromFolder(filePath));
            } else if (fileInfo.isFile()) {
                QString suffix = fileInfo.suffix().toLower();
                if (suffix == "mp3" || suffix == "wav" || suffix == "flac" || 
                    suffix == "ogg" || suffix == "m4a" || suffix == "aac") {
                    audioFiles.append(filePath);
                }
            }
        }
    }
    
    if (!audioFiles.isEmpty()) {
        addFiles(audioFiles);
        event->acceptProposedAction();
    }
}

void PlaylistWidget::onItemDoubleClicked(QListWidgetItem *item)
{
    int row = m_listWidget->row(item);
    QString filePath = m_playlist->trackAt(row);
    if (!filePath.isEmpty()) {
        m_playlist->setCurrentIndex(row);
        emit playTrack(filePath);
    }
}

void PlaylistWidget::onItemSelectionChanged()
{
    int currentRow = m_listWidget->currentRow();
    if (currentRow >= 0) {
        QString filePath = m_playlist->trackAt(currentRow);
        if (!filePath.isEmpty()) {
            emit trackSelected(filePath);
        }
    }
}

void PlaylistWidget::onTrackAdded(const QString &filePath)
{
    QString displayText = formatTrackInfo(filePath);
    QListWidgetItem *item = new QListWidgetItem(displayText);
    item->setData(Qt::UserRole, filePath);
    m_listWidget->addItem(item);
    
    updateTrackCount();
}

void PlaylistWidget::onTrackRemoved(int index)
{
    QListWidgetItem *item = m_listWidget->takeItem(index);
    delete item;
    
    updateTrackCount();
    updateCurrentTrackDisplay();
}

void PlaylistWidget::onTracksCleared()
{
    m_listWidget->clear();
    updateTrackCount();
}

void PlaylistWidget::onCurrentTrackChanged(const QString &filePath)
{
    m_currentTrackPath = filePath;
    updateCurrentTrackDisplay();
}

void PlaylistWidget::onPlaylistChanged()
{
    updateTrackCount();
}

// Context menu action slots
void PlaylistWidget::playSelectedTrack()
{
    int currentRow = m_listWidget->currentRow();
    if (currentRow >= 0) {
        QString filePath = m_playlist->trackAt(currentRow);
        if (!filePath.isEmpty()) {
            m_playlist->setCurrentIndex(currentRow);
            emit playTrack(filePath);
        }
    }
}

void PlaylistWidget::removeSelectedTrack()
{
    int currentRow = m_listWidget->currentRow();
    if (currentRow >= 0) {
        m_playlist->removeTrack(currentRow);
    }
}

void PlaylistWidget::clearPlaylist()
{
    if (m_playlist->count() > 0) {
        int ret = QMessageBox::question(this, "Clear Playlist",
                                       "Are you sure you want to clear the entire playlist?",
                                       QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes) {
            m_playlist->clear();
        }
    }
}

void PlaylistWidget::moveTrackUp()
{
    int currentRow = m_listWidget->currentRow();
    if (currentRow > 0) {
        m_playlist->moveTrack(currentRow, currentRow - 1);

        // Update list widget
        QListWidgetItem *item = m_listWidget->takeItem(currentRow);
        m_listWidget->insertItem(currentRow - 1, item);
        m_listWidget->setCurrentRow(currentRow - 1);
    }
}

void PlaylistWidget::moveTrackDown()
{
    int currentRow = m_listWidget->currentRow();
    if (currentRow >= 0 && currentRow < m_listWidget->count() - 1) {
        m_playlist->moveTrack(currentRow, currentRow + 1);

        // Update list widget
        QListWidgetItem *item = m_listWidget->takeItem(currentRow);
        m_listWidget->insertItem(currentRow + 1, item);
        m_listWidget->setCurrentRow(currentRow + 1);
    }
}

// Utility methods
void PlaylistWidget::updateTrackCount()
{
    int count = m_playlist->count();
    m_countLabel->setText(QString("%1 track%2").arg(count).arg(count != 1 ? "s" : ""));
}

void PlaylistWidget::updateCurrentTrackDisplay()
{
    // Highlight current track
    for (int i = 0; i < m_listWidget->count(); ++i) {
        QListWidgetItem *item = m_listWidget->item(i);
        QString filePath = item->data(Qt::UserRole).toString();

        if (filePath == m_currentTrackPath) {
            item->setBackground(QColor(66, 165, 245, 100)); // Semi-transparent blue
            item->setForeground(QColor(255, 255, 255));
        } else {
            item->setBackground(QColor());
            item->setForeground(QColor(255, 255, 255));
        }
    }
}

QStringList PlaylistWidget::getAudioFilesFromFolder(const QString &folderPath)
{
    QStringList audioFiles;
    QStringList supportedFormats = {"*.mp3", "*.wav", "*.flac", "*.ogg", "*.m4a", "*.aac"};

    QDirIterator iterator(folderPath, supportedFormats, QDir::Files, QDirIterator::Subdirectories);
    while (iterator.hasNext()) {
        audioFiles.append(iterator.next());
    }

    audioFiles.sort();
    return audioFiles;
}

QString PlaylistWidget::formatTrackInfo(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    QString title = m_playlist->getTrackTitle(filePath);
    QString artist = m_playlist->getTrackArtist(filePath);

    if (!artist.isEmpty()) {
        return QString("%1 - %2").arg(artist, title);
    } else {
        return title;
    }
}
