#ifndef TRACKSELECTIONDIALOG_H
#define TRACKSELECTIONDIALOG_H

#include <QDialog>
#include <QListWidget>
#include <QPushButton>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include "playlist.h"

class TrackSelectionDialog : public QDialog
{
    Q_OBJECT

public:
    explicit TrackSelectionDialog(Playlist *playlist, QWidget *parent = nullptr);
    QString selectedTrackPath() const;

private slots:
    void onSelectionChanged();
    void onItemDoubleClicked(QListWidgetItem *item);

private:
    void setupUI();
    void populateTrackList();

    Playlist *m_playlist;
    QListWidget *m_trackListWidget;
    QPushButton *m_editButton;
    QPushButton *m_cancelButton;
    QLabel *m_instructionLabel;
    
    QString m_selectedTrackPath;
};

#endif // TRACKSELECTIONDIALOG_H
