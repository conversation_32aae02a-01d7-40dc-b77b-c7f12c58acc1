#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QProgressBar>
#include <QStatusBar>
#include <QMenuBar>
#include <QAction>
#include <QFileDialog>
#include <QSplitter>
#include <QTimer>
#include <QKeySequence>
#include <QShortcut>
#include <QDragEnterEvent>
#include <QDropEvent>
#include <QMimeData>
#include <QUrl>

class AudioPlayer;
class PlaylistWidget;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void openFile();
    void openFolder();
    void togglePlayPause();
    void stopPlayback();
    void nextTrack();
    void previousTrack();
    void setVolume(int volume);
    void seekToPosition(int position);
    void updatePosition(qint64 position);
    void updateDuration(qint64 duration);
    void updateTrackInfo(const QString &title, const QString &artist, qint64 duration);
    void togglePlaylist();
    void toggleFullscreen();
    void onPlaybackStateChanged();
    void onMediaStatusChanged();
    void showError(const QString &error);

private:
    void setupUI();
    void setupMenuBar();
    void setupControlsLayout();
    void setupControlButtons();
    void setupVolumeControl();
    void setupShortcuts();
    void updatePlayPauseButton();
    void updateTimeLabels();
    void setButtonIcon(QPushButton *button, const QString &iconName);
    QString formatTime(qint64 milliseconds);
    void adaptToFullscreen();

    // UI Components
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QWidget *m_controlsWidget;
    PlaylistWidget *m_playlistWidget;
    
    // Control buttons
    QPushButton *m_playPauseButton;
    QPushButton *m_stopButton;
    QPushButton *m_previousButton;
    QPushButton *m_nextButton;
    QPushButton *m_volumeButton;
    QPushButton *m_playlistButton;
    
    // Sliders and progress
    QSlider *m_volumeSlider;
    QSlider *m_positionSlider;
    QProgressBar *m_progressBar;
    
    // Labels
    QLabel *m_trackTitleLabel;
    QLabel *m_trackArtistLabel;
    QLabel *m_currentTimeLabel;
    QLabel *m_totalTimeLabel;
    QLabel *m_volumeLabel;
    
    // Layouts
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_controlsLayout;
    QHBoxLayout *m_timeLayout;
    QHBoxLayout *m_volumeLayout;
    QGridLayout *m_buttonLayout;
    
    // Menu and actions
    QAction *m_openFileAction;
    QAction *m_openFolderAction;
    QAction *m_exitAction;
    QAction *m_togglePlaylistAction;
    QAction *m_fullscreenAction;
    
    // Shortcuts
    QShortcut *m_playPauseShortcut;
    QShortcut *m_stopShortcut;
    QShortcut *m_nextShortcut;
    QShortcut *m_previousShortcut;
    QShortcut *m_volumeUpShortcut;
    QShortcut *m_volumeDownShortcut;
    
    // Core components
    AudioPlayer *m_audioPlayer;
    
    // State
    bool m_playlistVisible;
    bool m_isFullscreen;
    qint64 m_duration;
    bool m_positionSliderPressed;
};

#endif // MAINWINDOW_H
