@echo off
echo Testing Qt Audio Player Project Structure...
echo.

echo Checking required files:
if exist "AudioPlayer.pro" (
    echo [OK] AudioPlayer.pro found
) else (
    echo [ERROR] AudioPlayer.pro missing
)

if exist "main.cpp" (
    echo [OK] main.cpp found
) else (
    echo [ERROR] main.cpp missing
)

if exist "mainwindow.h" (
    echo [OK] mainwindow.h found
) else (
    echo [ERROR] mainwindow.h missing
)

if exist "mainwindow.cpp" (
    echo [OK] mainwindow.cpp found
) else (
    echo [ERROR] mainwindow.cpp missing
)

if exist "audioplayer.h" (
    echo [OK] audioplayer.h found
) else (
    echo [ERROR] audioplayer.h missing
)

if exist "audioplayer.cpp" (
    echo [OK] audioplayer.cpp found
) else (
    echo [ERROR] audioplayer.cpp missing
)

if exist "playlist.h" (
    echo [OK] playlist.h found
) else (
    echo [ERROR] playlist.h missing
)

if exist "playlist.cpp" (
    echo [OK] playlist.cpp found
) else (
    echo [ERROR] playlist.cpp missing
)

if exist "playlistwidget.h" (
    echo [OK] playlistwidget.h found
) else (
    echo [ERROR] playlistwidget.h missing
)

if exist "playlistwidget.cpp" (
    echo [OK] playlistwidget.cpp found
) else (
    echo [ERROR] playlistwidget.cpp missing
)

if exist "resources.qrc" (
    echo [OK] resources.qrc found
) else (
    echo [ERROR] resources.qrc missing
)

echo.
echo Checking icons directory:
if exist "icons" (
    echo [OK] icons directory found
    dir icons\*.svg /b 2>nul | find /c ".svg" > temp_count.txt
    set /p icon_count=<temp_count.txt
    del temp_count.txt
    echo [INFO] Found SVG icons in icons directory
) else (
    echo [ERROR] icons directory missing
)

echo.
echo Project structure verification complete!
echo.
echo To build the project:
echo 1. Open Qt Creator
echo 2. Open AudioPlayer.pro
echo 3. Configure with Qt 6.x kit
echo 4. Build and run
echo.
pause
