#ifndef AUDIOTIMELINEWIDGET_H
#define AUDIOTIMELINEWIDGET_H

#include <QWidget>
#include <QPainter>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QTimer>
#include <QVector>

class AudioTimelineWidget : public QWidget
{
    Q_OBJECT

public:
    explicit AudioTimelineWidget(QWidget *parent = nullptr);
    
    void setDuration(qint64 duration);
    void setPosition(qint64 position);
    void setZoomLevel(double zoom);
    void setSelection(qint64 start, qint64 end);
    void clearSelection();
    void selectAll();
    
    qint64 duration() const { return m_duration; }
    qint64 position() const { return m_position; }
    double zoomLevel() const { return m_zoomLevel; }
    bool hasSelection() const { return m_selectionStart != -1 && m_selectionEnd != -1; }
    qint64 selectionStart() const { return m_selectionStart; }
    qint64 selectionEnd() const { return m_selectionEnd; }
    
    QSize sizeHint() const override;
    QSize minimumSizeHint() const override;

signals:
    void positionClicked(qint64 position);
    void selectionChanged(qint64 start, qint64 end);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private:
    void drawWaveform(QPainter &painter);
    void drawTimeline(QPainter &painter);
    void drawPlayhead(QPainter &painter);
    void drawSelection(QPainter &painter);
    void drawTimeMarkers(QPainter &painter);
    
    qint64 pixelToTime(int pixel) const;
    int timeToPixel(qint64 time) const;
    void updateWaveform();
    void generateDummyWaveform(); // For now, until we implement real waveform analysis
    
    // Audio data
    qint64 m_duration;
    qint64 m_position;
    QVector<float> m_waveformData;
    
    // Display properties
    double m_zoomLevel;
    int m_waveformHeight;
    int m_timelineHeight;
    int m_markerHeight;
    
    // Selection
    qint64 m_selectionStart;
    qint64 m_selectionEnd;
    bool m_isSelecting;
    int m_selectionStartPixel;
    
    // Mouse interaction
    bool m_isDragging;
    QPoint m_lastMousePos;
    
    // Colors
    QColor m_backgroundColor;
    QColor m_waveformColor;
    QColor m_playheadColor;
    QColor m_selectionColor;
    QColor m_timelineColor;
    QColor m_textColor;
};

#endif // AUDIOTIMELINEWIDGET_H
