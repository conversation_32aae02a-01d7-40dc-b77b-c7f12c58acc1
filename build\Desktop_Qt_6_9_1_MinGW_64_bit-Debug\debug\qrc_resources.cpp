/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.9.1
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#ifdef _MSC_VER
// disable informational message "function ... selected for automatic inline expansion"
#pragma warning (disable: 4711)
#endif

static const unsigned char qt_resource_data[] = {
  // volume-mute.svg
  0x0,0x0,0x1,0x65,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,
  0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x32,0x33,0x20,0x39,0x4c,0x31,0x37,0x20,0x31,
  0x35,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,
  0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,
  0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x37,0x20,0x39,0x4c,0x32,0x33,0x20,
  0x31,0x35,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,0x74,
  0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,
  0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,
  0x76,0x67,0x3e,0xa,
    // play.svg
  0x0,0x0,0x0,0xf4,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x38,0x20,0x35,0x56,0x31,0x39,0x4c,0x31,0x39,0x20,0x31,0x32,0x4c,0x38,
  0x20,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x6a,0x6f,0x69,
  0x6e,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,
  0x67,0x3e,0xa,
    // album-default.svg
  0x0,0x0,0x5,0xbc,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,
  0x20,0x32,0x30,0x30,0x20,0x32,0x30,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x6e,0x6f,0x6e,0x65,0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,
  0x70,0x3a,0x2f,0x2f,0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,
  0x30,0x30,0x30,0x2f,0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,
  0x20,0x42,0x61,0x63,0x6b,0x67,0x72,0x6f,0x75,0x6e,0x64,0x20,0x2d,0x2d,0x3e,0xa,
  0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x30,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x30,0x30,0x22,
  0x20,0x72,0x78,0x3d,0x22,0x31,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,
  0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,
  0x74,0x20,0x78,0x3d,0x22,0x35,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,0x69,
  0x64,0x74,0x68,0x3d,0x22,0x31,0x39,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,
  0x3d,0x22,0x31,0x39,0x30,0x22,0x20,0x72,0x78,0x3d,0x22,0x38,0x22,0x20,0x66,0x69,
  0x6c,0x6c,0x3d,0x22,0x23,0x33,0x61,0x33,0x61,0x33,0x61,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x56,0x69,0x6e,0x79,0x6c,0x20,0x72,
  0x65,0x63,0x6f,0x72,0x64,0x20,0x64,0x65,0x73,0x69,0x67,0x6e,0x20,0x2d,0x2d,0x3e,
  0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,
  0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,
  0x38,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,
  0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,
  0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,
  0x20,0x72,0x3d,0x22,0x37,0x35,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,
  0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x34,0x30,0x34,
  0x30,0x34,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x31,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,
  0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,
  0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x36,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,
  0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,
  0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,
  0x69,0x64,0x74,0x68,0x3d,0x22,0x30,0x2e,0x35,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,
  0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,
  0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x34,0x35,0x22,0x20,
  0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,
  0x6b,0x65,0x3d,0x22,0x23,0x34,0x30,0x34,0x30,0x34,0x30,0x22,0x20,0x73,0x74,0x72,
  0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x30,0x2e,0x35,0x22,0x2f,
  0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,
  0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,
  0x22,0x33,0x30,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x23,0x34,0x30,0x34,0x30,0x34,0x30,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,
  0x30,0x2e,0x35,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,
  0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x30,
  0x30,0x22,0x20,0x72,0x3d,0x22,0x31,0x35,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,
  0x23,0x32,0x61,0x32,0x61,0x32,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x63,0x69,
  0x72,0x63,0x6c,0x65,0x20,0x63,0x78,0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x63,0x79,
  0x3d,0x22,0x31,0x30,0x30,0x22,0x20,0x72,0x3d,0x22,0x38,0x22,0x20,0x66,0x69,0x6c,
  0x6c,0x3d,0x22,0x23,0x31,0x61,0x31,0x61,0x31,0x61,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0xa,0x20,0x20,0x3c,0x21,0x2d,0x2d,0x20,0x4d,0x75,0x73,0x69,0x63,0x20,0x6e,0x6f,
  0x74,0x65,0x20,0x69,0x63,0x6f,0x6e,0x20,0x69,0x6e,0x20,0x63,0x65,0x6e,0x74,0x65,
  0x72,0x20,0x2d,0x2d,0x3e,0xa,0x20,0x20,0x3c,0x67,0x20,0x74,0x72,0x61,0x6e,0x73,
  0x66,0x6f,0x72,0x6d,0x3d,0x22,0x74,0x72,0x61,0x6e,0x73,0x6c,0x61,0x74,0x65,0x28,
  0x38,0x35,0x2c,0x20,0x38,0x30,0x29,0x22,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x70,
  0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x32,0x20,0x33,0x56,0x31,0x33,0x2e,
  0x35,0x35,0x43,0x31,0x31,0x2e,0x34,0x31,0x20,0x31,0x33,0x2e,0x32,0x31,0x20,0x31,
  0x30,0x2e,0x37,0x33,0x20,0x31,0x33,0x20,0x31,0x30,0x20,0x31,0x33,0x43,0x37,0x2e,
  0x37,0x39,0x20,0x31,0x33,0x20,0x36,0x20,0x31,0x34,0x2e,0x37,0x39,0x20,0x36,0x20,
  0x31,0x37,0x53,0x37,0x2e,0x37,0x39,0x20,0x32,0x31,0x20,0x31,0x30,0x20,0x32,0x31,
  0x20,0x31,0x34,0x20,0x31,0x39,0x2e,0x32,0x31,0x20,0x31,0x34,0x20,0x31,0x37,0x56,
  0x37,0x48,0x31,0x38,0x56,0x35,0x48,0x31,0x34,0x56,0x33,0x48,0x31,0x32,0x5a,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x34,0x32,0x61,0x35,0x66,0x35,0x22,0x2f,
  0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,0x63,0x69,0x72,0x63,0x6c,0x65,0x20,0x63,0x78,
  0x3d,0x22,0x31,0x30,0x22,0x20,0x63,0x79,0x3d,0x22,0x31,0x37,0x22,0x20,0x72,0x3d,
  0x22,0x32,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x23,0x31,0x39,0x37,0x36,0x64,
  0x32,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x2f,0x67,0x3e,0xa,0x20,0x20,0xa,0x20,
  0x20,0x3c,0x21,0x2d,0x2d,0x20,0x53,0x75,0x62,0x74,0x6c,0x65,0x20,0x67,0x72,0x61,
  0x64,0x69,0x65,0x6e,0x74,0x20,0x6f,0x76,0x65,0x72,0x6c,0x61,0x79,0x20,0x2d,0x2d,
  0x3e,0xa,0x20,0x20,0x3c,0x64,0x65,0x66,0x73,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,
  0x72,0x61,0x64,0x69,0x61,0x6c,0x47,0x72,0x61,0x64,0x69,0x65,0x6e,0x74,0x20,0x69,
  0x64,0x3d,0x22,0x73,0x68,0x69,0x6e,0x65,0x22,0x20,0x63,0x78,0x3d,0x22,0x30,0x2e,
  0x33,0x22,0x20,0x63,0x79,0x3d,0x22,0x30,0x2e,0x33,0x22,0x20,0x72,0x3d,0x22,0x30,
  0x2e,0x37,0x22,0x3e,0xa,0x20,0x20,0x20,0x20,0x20,0x20,0x3c,0x73,0x74,0x6f,0x70,
  0x20,0x6f,0x66,0x66,0x73,0x65,0x74,0x3d,0x22,0x30,0x25,0x22,0x20,0x73,0x74,0x79,
  0x6c,0x65,0x3d,0x22,0x73,0x74,0x6f,0x70,0x2d,0x63,0x6f,0x6c,0x6f,0x72,0x3a,0x72,
  0x67,0x62,0x61,0x28,0x32,0x35,0x35,0x2c,0x32,0x35,0x35,0x2c,0x32,0x35,0x35,0x2c,
  0x30,0x2e,0x31,0x29,0x3b,0x73,0x74,0x6f,0x70,0x2d,0x6f,0x70,0x61,0x63,0x69,0x74,
  0x79,0x3a,0x31,0x22,0x20,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x20,0x20,0x3c,0x73,
  0x74,0x6f,0x70,0x20,0x6f,0x66,0x66,0x73,0x65,0x74,0x3d,0x22,0x31,0x30,0x30,0x25,
  0x22,0x20,0x73,0x74,0x79,0x6c,0x65,0x3d,0x22,0x73,0x74,0x6f,0x70,0x2d,0x63,0x6f,
  0x6c,0x6f,0x72,0x3a,0x72,0x67,0x62,0x61,0x28,0x32,0x35,0x35,0x2c,0x32,0x35,0x35,
  0x2c,0x32,0x35,0x35,0x2c,0x30,0x29,0x3b,0x73,0x74,0x6f,0x70,0x2d,0x6f,0x70,0x61,
  0x63,0x69,0x74,0x79,0x3a,0x30,0x22,0x20,0x2f,0x3e,0xa,0x20,0x20,0x20,0x20,0x3c,
  0x2f,0x72,0x61,0x64,0x69,0x61,0x6c,0x47,0x72,0x61,0x64,0x69,0x65,0x6e,0x74,0x3e,
  0xa,0x20,0x20,0x3c,0x2f,0x64,0x65,0x66,0x73,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,
  0x63,0x74,0x20,0x78,0x3d,0x22,0x35,0x22,0x20,0x79,0x3d,0x22,0x35,0x22,0x20,0x77,
  0x69,0x64,0x74,0x68,0x3d,0x22,0x31,0x39,0x30,0x22,0x20,0x68,0x65,0x69,0x67,0x68,
  0x74,0x3d,0x22,0x31,0x39,0x30,0x22,0x20,0x72,0x78,0x3d,0x22,0x38,0x22,0x20,0x66,
  0x69,0x6c,0x6c,0x3d,0x22,0x75,0x72,0x6c,0x28,0x23,0x73,0x68,0x69,0x6e,0x65,0x29,
  0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // playlist.svg
  0x0,0x0,0x2,0x66,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x38,0x20,0x36,0x48,0x32,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,
  0x20,0x31,0x32,0x48,0x32,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,
  0x77,0x68,0x69,0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,
  0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,
  0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,
  0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x38,0x20,0x31,
  0x38,0x48,0x32,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,
  0x69,0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,
  0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,
  0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,
  0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x33,0x20,0x36,0x48,0x33,
  0x2e,0x30,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,
  0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x33,0x20,0x31,0x32,0x48,0x33,
  0x2e,0x30,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,
  0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x20,0x20,
  0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x33,0x20,0x31,0x38,0x48,0x33,
  0x2e,0x30,0x31,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,
  0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,
  0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // pause.svg
  0x0,0x0,0x0,0xe6,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x36,0x20,0x34,0x48,0x31,0x30,0x56,0x32,0x30,0x48,0x36,0x56,0x34,0x5a,
  0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x2f,0x3e,
  0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x34,0x20,
  0x34,0x48,0x31,0x38,0x56,0x32,0x30,0x48,0x31,0x34,0x56,0x34,0x5a,0x22,0x20,0x66,
  0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x2f,0x3e,0xa,0x3c,0x2f,
  0x73,0x76,0x67,0x3e,0xa,
    // previous.svg
  0x0,0x0,0x1,0xe,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x36,0x20,0x36,0x56,0x31,0x38,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x39,
  0x2e,0x35,0x20,0x31,0x32,0x4c,0x31,0x38,0x20,0x36,0x56,0x31,0x38,0x4c,0x39,0x2e,
  0x35,0x20,0x31,0x32,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,
  0x74,0x65,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // volume.svg
  0x0,0x0,0x2,0x13,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,
  0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x39,0x2e,0x30,0x37,0x20,0x34,0x2e,0x39,
  0x33,0x43,0x32,0x30,0x2e,0x39,0x34,0x34,0x35,0x20,0x36,0x2e,0x38,0x30,0x34,0x35,
  0x35,0x20,0x32,0x31,0x2e,0x39,0x39,0x38,0x32,0x20,0x39,0x2e,0x33,0x34,0x38,0x36,
  0x39,0x20,0x32,0x31,0x2e,0x39,0x39,0x38,0x32,0x20,0x31,0x32,0x43,0x32,0x31,0x2e,
  0x39,0x39,0x38,0x32,0x20,0x31,0x34,0x2e,0x36,0x35,0x31,0x33,0x20,0x32,0x30,0x2e,
  0x39,0x34,0x34,0x35,0x20,0x31,0x37,0x2e,0x31,0x39,0x35,0x35,0x20,0x31,0x39,0x2e,
  0x30,0x37,0x20,0x31,0x39,0x2e,0x30,0x37,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,
  0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,
  0x35,0x2e,0x35,0x34,0x20,0x38,0x2e,0x34,0x36,0x43,0x31,0x36,0x2e,0x34,0x37,0x37,
  0x34,0x20,0x39,0x2e,0x33,0x39,0x37,0x36,0x34,0x20,0x31,0x37,0x2e,0x30,0x30,0x33,
  0x39,0x20,0x31,0x30,0x2e,0x36,0x36,0x39,0x32,0x20,0x31,0x37,0x2e,0x30,0x30,0x33,
  0x39,0x20,0x31,0x32,0x43,0x31,0x37,0x2e,0x30,0x30,0x33,0x39,0x20,0x31,0x33,0x2e,
  0x33,0x33,0x30,0x38,0x20,0x31,0x36,0x2e,0x34,0x37,0x37,0x34,0x20,0x31,0x34,0x2e,
  0x36,0x30,0x32,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,
  0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,
  0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,
  0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,
  0x3e,0xa,
    // next.svg
  0x0,0x0,0x1,0x10,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x38,0x20,0x36,0x56,0x31,0x38,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,
  0x65,0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,
  0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,
  0x31,0x34,0x2e,0x35,0x20,0x31,0x32,0x4c,0x36,0x20,0x31,0x38,0x56,0x36,0x4c,0x31,
  0x34,0x2e,0x35,0x20,0x31,0x32,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,
  0x68,0x69,0x74,0x65,0x22,0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // volume-low.svg
  0x0,0x0,0x1,0x6b,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,0x74,0x68,0x20,0x64,0x3d,
  0x22,0x4d,0x31,0x31,0x20,0x35,0x4c,0x36,0x20,0x39,0x48,0x32,0x56,0x31,0x35,0x48,
  0x36,0x4c,0x31,0x31,0x20,0x31,0x39,0x56,0x35,0x5a,0x22,0x20,0x66,0x69,0x6c,0x6c,
  0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x2f,0x3e,0xa,0x20,0x20,0x3c,0x70,0x61,
  0x74,0x68,0x20,0x64,0x3d,0x22,0x4d,0x31,0x35,0x2e,0x35,0x34,0x20,0x38,0x2e,0x34,
  0x36,0x43,0x31,0x36,0x2e,0x34,0x37,0x37,0x34,0x20,0x39,0x2e,0x33,0x39,0x37,0x36,
  0x34,0x20,0x31,0x37,0x2e,0x30,0x30,0x33,0x39,0x20,0x31,0x30,0x2e,0x36,0x36,0x39,
  0x32,0x20,0x31,0x37,0x2e,0x30,0x30,0x33,0x39,0x20,0x31,0x32,0x43,0x31,0x37,0x2e,
  0x30,0x30,0x33,0x39,0x20,0x31,0x33,0x2e,0x33,0x33,0x30,0x38,0x20,0x31,0x36,0x2e,
  0x34,0x37,0x37,0x34,0x20,0x31,0x34,0x2e,0x36,0x30,0x32,0x34,0x20,0x31,0x35,0x2e,
  0x35,0x34,0x20,0x31,0x35,0x2e,0x35,0x34,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,0x2d,
  0x77,0x69,0x64,0x74,0x68,0x3d,0x22,0x32,0x22,0x20,0x73,0x74,0x72,0x6f,0x6b,0x65,
  0x2d,0x6c,0x69,0x6e,0x65,0x63,0x61,0x70,0x3d,0x22,0x72,0x6f,0x75,0x6e,0x64,0x22,
  0x2f,0x3e,0xa,0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
    // stop.svg
  0x0,0x0,0x0,0xc8,
  0x3c,
  0x3f,0x78,0x6d,0x6c,0x20,0x76,0x65,0x72,0x73,0x69,0x6f,0x6e,0x3d,0x22,0x31,0x2e,
  0x30,0x22,0x20,0x65,0x6e,0x63,0x6f,0x64,0x69,0x6e,0x67,0x3d,0x22,0x55,0x54,0x46,
  0x2d,0x38,0x22,0x3f,0x3e,0xa,0x3c,0x73,0x76,0x67,0x20,0x77,0x69,0x64,0x74,0x68,
  0x3d,0x22,0x32,0x34,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x32,0x34,
  0x22,0x20,0x76,0x69,0x65,0x77,0x42,0x6f,0x78,0x3d,0x22,0x30,0x20,0x30,0x20,0x32,
  0x34,0x20,0x32,0x34,0x22,0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x6e,0x6f,0x6e,0x65,
  0x22,0x20,0x78,0x6d,0x6c,0x6e,0x73,0x3d,0x22,0x68,0x74,0x74,0x70,0x3a,0x2f,0x2f,
  0x77,0x77,0x77,0x2e,0x77,0x33,0x2e,0x6f,0x72,0x67,0x2f,0x32,0x30,0x30,0x30,0x2f,
  0x73,0x76,0x67,0x22,0x3e,0xa,0x20,0x20,0x3c,0x72,0x65,0x63,0x74,0x20,0x78,0x3d,
  0x22,0x36,0x22,0x20,0x79,0x3d,0x22,0x36,0x22,0x20,0x77,0x69,0x64,0x74,0x68,0x3d,
  0x22,0x31,0x32,0x22,0x20,0x68,0x65,0x69,0x67,0x68,0x74,0x3d,0x22,0x31,0x32,0x22,
  0x20,0x66,0x69,0x6c,0x6c,0x3d,0x22,0x77,0x68,0x69,0x74,0x65,0x22,0x2f,0x3e,0xa,
  0x3c,0x2f,0x73,0x76,0x67,0x3e,0xa,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // volume-mute.svg
  0x0,0xf,
  0xb,0x23,0x2f,0xc7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2d,0x0,0x6d,0x0,0x75,0x0,0x74,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // play.svg
  0x0,0x8,
  0x2,0x8c,0x54,0x27,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // album-default.svg
  0x0,0x11,
  0xd,0xe2,0x84,0xc7,
  0x0,0x61,
  0x0,0x6c,0x0,0x62,0x0,0x75,0x0,0x6d,0x0,0x2d,0x0,0x64,0x0,0x65,0x0,0x66,0x0,0x61,0x0,0x75,0x0,0x6c,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
    // playlist.svg
  0x0,0xc,
  0xe,0x42,0x7a,0xa7,
  0x0,0x70,
  0x0,0x6c,0x0,0x61,0x0,0x79,0x0,0x6c,0x0,0x69,0x0,0x73,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // pause.svg
  0x0,0x9,
  0xc,0x98,0xb7,0xc7,
  0x0,0x70,
  0x0,0x61,0x0,0x75,0x0,0x73,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // previous.svg
  0x0,0xc,
  0x8,0x37,0xc0,0xc7,
  0x0,0x70,
  0x0,0x72,0x0,0x65,0x0,0x76,0x0,0x69,0x0,0x6f,0x0,0x75,0x0,0x73,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume.svg
  0x0,0xa,
  0xc,0x3b,0xf6,0xa7,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // next.svg
  0x0,0x8,
  0xc,0xf7,0x54,0x47,
  0x0,0x6e,
  0x0,0x65,0x0,0x78,0x0,0x74,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // volume-low.svg
  0x0,0xe,
  0xf,0x12,0x2d,0x87,
  0x0,0x76,
  0x0,0x6f,0x0,0x6c,0x0,0x75,0x0,0x6d,0x0,0x65,0x0,0x2d,0x0,0x6c,0x0,0x6f,0x0,0x77,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
    // stop.svg
  0x0,0x8,
  0xb,0x63,0x55,0x87,
  0x0,0x73,
  0x0,0x74,0x0,0x6f,0x0,0x70,0x0,0x2e,0x0,0x73,0x0,0x76,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xa,0x0,0x0,0x0,0x3,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/icons/play.svg
  0x0,0x0,0x0,0x34,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x1,0x69,
0x0,0x0,0x1,0x97,0xd6,0x28,0x89,0x40,
  // :/icons/icons/previous.svg
  0x0,0x0,0x0,0xa8,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xb,0x75,
0x0,0x0,0x1,0x97,0xd6,0x28,0xc9,0xb1,
  // :/icons/icons/volume-mute.svg
  0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0xd6,0x29,0x43,0x81,
  // :/icons/icons/stop.svg
  0x0,0x0,0x1,0x18,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x11,0x21,
0x0,0x0,0x1,0x97,0xd6,0x28,0xb2,0x1,
  // :/icons/icons/volume.svg
  0x0,0x0,0x0,0xc6,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xc,0x87,
0x0,0x0,0x1,0x97,0xd6,0x29,0xf,0xdf,
  // :/icons/icons/pause.svg
  0x0,0x0,0x0,0x90,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xa,0x8b,
0x0,0x0,0x1,0x97,0xd6,0x28,0x9f,0x38,
  // :/icons/icons/next.svg
  0x0,0x0,0x0,0xe0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xe,0x9e,
0x0,0x0,0x1,0x97,0xd6,0x28,0xde,0xef,
  // :/icons/icons/album-default.svg
  0x0,0x0,0x0,0x4a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x2,0x61,
0x0,0x0,0x1,0x97,0xd6,0x34,0x3a,0xeb,
  // :/icons/icons/playlist.svg
  0x0,0x0,0x0,0x72,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x8,0x21,
0x0,0x0,0x1,0x97,0xd6,0x29,0x5b,0xc7,
  // :/icons/icons/volume-low.svg
  0x0,0x0,0x0,0xf6,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0xf,0xb2,
0x0,0x0,0x1,0x97,0xd6,0x29,0x28,0xe7,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#if defined(QT_INLINE_NAMESPACE)
inline namespace QT_NAMESPACE {
#elif defined(QT_NAMESPACE)
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
