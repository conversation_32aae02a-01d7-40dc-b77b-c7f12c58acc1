#ifndef AUDIOPLAYER_H
#define AUDIOPLAYER_H

#include <QObject>
#include <QMediaPlayer>
#include <QAudioOutput>
#include <QUrl>
#include <QString>
#include <QFileInfo>
#include <QTimer>

class Playlist;

class AudioPlayer : public QObject
{
    Q_OBJECT

public:
    explicit AudioPlayer(QObject *parent = nullptr);
    ~AudioPlayer();

    // Playback control
    void play();
    void pause();
    void stop();
    void setPosition(qint64 position);
    void setVolume(float volume);
    
    // Track management
    void setCurrentTrack(const QString &filePath);
    void setPlaylist(Playlist *playlist);
    
    // State queries
    bool isPlaying() const;
    bool isPaused() const;
    bool isStopped() const;
    qint64 position() const;
    qint64 duration() const;
    float volume() const;
    QString currentTrack() const;
    
    // Media information
    QString getTrackTitle(const QString &filePath) const;
    QString getTrackArtist(const QString &filePath) const;
    qint64 getTrackDuration(const QString &filePath) const;

signals:
    void positionChanged(qint64 position);
    void durationChanged(qint64 duration);
    void trackInfoChanged(const QString &title, const QString &artist, qint64 duration);
    void playbackStateChanged();
    void mediaStatusChanged();
    void trackChanged(const QString &filePath);
    void errorOccurred(const QString &error);

private slots:
    void onPositionChanged(qint64 position);
    void onDurationChanged(qint64 duration);
    void onPlaybackStateChanged(QMediaPlayer::PlaybackState state);
    void onMediaStatusChanged(QMediaPlayer::MediaStatus status);
    void onErrorOccurred(QMediaPlayer::Error error, const QString &errorString);

private:
    void extractTrackInfo(const QString &filePath);
    QString extractTitleFromFileName(const QString &filePath) const;
    bool isValidAudioFile(const QString &filePath) const;

    QMediaPlayer *m_mediaPlayer;
    QAudioOutput *m_audioOutput;
    Playlist *m_playlist;
    QString m_currentTrackPath;
    QString m_currentTitle;
    QString m_currentArtist;
    qint64 m_currentDuration;
    float m_currentVolume;
    
    // Supported audio formats
    QStringList m_supportedFormats;
};

#endif // AUDIOPLAYER_H
