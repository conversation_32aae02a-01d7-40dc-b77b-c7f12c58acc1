#include "trackselectiondialog.h"
#include <QFileInfo>

TrackSelectionDialog::TrackSelectionDialog(Playlist *playlist, QWidget *parent)
    : QDialog(parent)
    , m_playlist(playlist)
    , m_trackListWidget(nullptr)
    , m_edit<PERSON>utton(nullptr)
    , m_cancelButton(nullptr)
    , m_instructionLabel(nullptr)
{
    setWindowTitle("Select Track to Edit");
    setModal(true);
    resize(500, 400);
    
    setupUI();
    populateTrackList();
}

QString TrackSelectionDialog::selectedTrackPath() const
{
    return m_selectedTrackPath;
}

void TrackSelectionDialog::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(15);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    
    // Instruction label
    m_instructionLabel = new QLabel("Select a track from your playlist to edit:");
    m_instructionLabel->setStyleSheet("font-size: 14px; color: #cccccc; margin-bottom: 10px;");
    mainLayout->addWidget(m_instructionLabel);
    
    // Track list widget
    m_trackListWidget = new QListWidget;
    m_trackListWidget->setStyleSheet(
        "QListWidget {"
        "    background-color: #2a2a2a;"
        "    border: 2px solid #555555;"
        "    border-radius: 5px;"
        "    color: #ffffff;"
        "    font-size: 12px;"
        "    selection-background-color: #42a5f5;"
        "    selection-color: #ffffff;"
        "}"
        "QListWidget::item {"
        "    padding: 8px;"
        "    border-bottom: 1px solid #444444;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: #3a3a3a;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #42a5f5;"
        "}"
    );
    
    connect(m_trackListWidget, &QListWidget::itemSelectionChanged, 
            this, &TrackSelectionDialog::onSelectionChanged);
    connect(m_trackListWidget, &QListWidget::itemDoubleClicked,
            this, &TrackSelectionDialog::onItemDoubleClicked);
    
    mainLayout->addWidget(m_trackListWidget);
    
    // Button layout
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    buttonLayout->addStretch();
    
    m_cancelButton = new QPushButton("Cancel");
    m_cancelButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #555555;"
        "    color: #ffffff;"
        "    border: none;"
        "    border-radius: 5px;"
        "    padding: 8px 20px;"
        "    font-size: 12px;"
        "}"
        "QPushButton:hover {"
        "    background-color: #666666;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #444444;"
        "}"
    );
    connect(m_cancelButton, &QPushButton::clicked, this, &QDialog::reject);
    buttonLayout->addWidget(m_cancelButton);
    
    m_editButton = new QPushButton("Edit Selected Track");
    m_editButton->setStyleSheet(
        "QPushButton {"
        "    background-color: #42a5f5;"
        "    color: #ffffff;"
        "    border: none;"
        "    border-radius: 5px;"
        "    padding: 8px 20px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #1976d2;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #0d47a1;"
        "}"
        "QPushButton:disabled {"
        "    background-color: #666666;"
        "    color: #999999;"
        "}"
    );
    m_editButton->setEnabled(false);
    connect(m_editButton, &QPushButton::clicked, this, &QDialog::accept);
    buttonLayout->addWidget(m_editButton);
    
    mainLayout->addLayout(buttonLayout);
    
    // Set dialog style
    setStyleSheet(
        "QDialog {"
        "    background-color: #1a1a1a;"
        "    color: #ffffff;"
        "}"
    );
}

void TrackSelectionDialog::populateTrackList()
{
    if (!m_playlist) return;

    for (int i = 0; i < m_playlist->count(); ++i) {
        QString filePath = m_playlist->trackAt(i);
        QFileInfo fileInfo(filePath);

        QString artist = m_playlist->getTrackArtist(filePath);
        QString title = m_playlist->getTrackTitle(filePath);

        QString displayText = QString("%1. %2 - %3")
                             .arg(i + 1, 2, 10, QChar('0'))
                             .arg(artist.isEmpty() ? "Unknown Artist" : artist)
                             .arg(title.isEmpty() ? fileInfo.baseName() : title);

        QListWidgetItem *item = new QListWidgetItem(displayText);
        item->setData(Qt::UserRole, filePath);
        m_trackListWidget->addItem(item);
    }
}

void TrackSelectionDialog::onSelectionChanged()
{
    QListWidgetItem *currentItem = m_trackListWidget->currentItem();
    if (currentItem) {
        m_selectedTrackPath = currentItem->data(Qt::UserRole).toString();
        m_editButton->setEnabled(true);
    } else {
        m_selectedTrackPath.clear();
        m_editButton->setEnabled(false);
    }
}

void TrackSelectionDialog::onItemDoubleClicked(QListWidgetItem *item)
{
    if (item) {
        m_selectedTrackPath = item->data(Qt::UserRole).toString();
        accept();
    }
}
