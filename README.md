# Qt Audio Player

A modern, feature-rich audio player application built with Qt 6.x and C++. This application provides a clean, dark-themed interface for playing various audio formats with comprehensive playlist management.

## Features

### Core Functionality
- **Multi-format Audio Support**: MP3, WAV, FLAC, OGG, M4A, AAC
- **Intuitive Playback Controls**: Play, pause, stop, next, previous
- **Volume Control**: Adjustable volume with visual feedback
- **Progress Control**: Seek bar with time display (current/total duration)
- **Track Information**: Display of title, artist, and duration

### User Interface
- **Modern Dark Theme**: Sleek dark interface with blue accent colors
- **Responsive Design**: Adapts to fullscreen mode
- **Custom SVG Icons**: Professional-looking vector icons for all controls
- **Playlist Panel**: Toggleable playlist view with track management
- **Status Bar**: Current track information and system messages

### Playlist Management
- **Add Files**: Individual file selection or batch import
- **Add Folders**: Recursive folder scanning for audio files
- **Drag & Drop**: Direct file/folder dropping into the application
- **Reorder Tracks**: Move tracks up/down in the playlist
- **Remove Tracks**: Delete individual tracks or clear entire playlist
- **Track Navigation**: Double-click to play, keyboard navigation

### Advanced Features
- **Keyboard Shortcuts**: Full keyboard control for all operations
- **Context Menus**: Right-click menus for playlist operations
- **File Validation**: Automatic filtering of supported audio formats
- **Error Handling**: Graceful handling of invalid files and playback errors
- **Fullscreen Mode**: Distraction-free listening experience

## System Requirements

- **Qt Version**: Qt 6.x or later
- **Compiler**: C++17 compatible compiler (GCC, Clang, MSVC)
- **Operating System**: Windows, macOS, or Linux
- **Dependencies**: Qt Multimedia module

## Building the Application

### Prerequisites
1. Install Qt 6.x with the following components:
   - Qt Core
   - Qt Widgets
   - Qt Multimedia
   - Qt SVG (for icon support)

2. Ensure you have a C++17 compatible compiler installed

### Build Instructions

#### Using Qt Creator (Recommended)
1. Open Qt Creator
2. Click "Open Project" and select `AudioPlayer.pro`
3. Configure the project with your Qt 6.x kit
4. Build the project (Ctrl+B or Build menu)
5. Run the application (Ctrl+R or Run menu)

#### Using Command Line (qmake)
```bash
# Navigate to the project directory
cd /path/to/AudioPlayer

# Generate Makefile
qmake AudioPlayer.pro

# Build the application
make

# Run the application
./AudioPlayer  # Linux/macOS
AudioPlayer.exe  # Windows
```

#### Using Command Line (CMake - Alternative)
If you prefer CMake, you can create a CMakeLists.txt file:
```bash
mkdir build
cd build
cmake ..
make
./AudioPlayer
```

## Usage Guide

### Getting Started
1. Launch the application
2. Add audio files using one of these methods:
   - **File Menu**: File → Open File or Open Folder
   - **Drag & Drop**: Drag audio files/folders directly into the window
   - **Keyboard**: Ctrl+O (file) or Ctrl+Shift+O (folder)

### Playback Controls
- **Play/Pause**: Click the play button or press Spacebar
- **Stop**: Click stop button or press Ctrl+S
- **Next Track**: Click next button or press Right arrow
- **Previous Track**: Click previous button or press Left arrow
- **Volume**: Use volume slider or Up/Down arrow keys
- **Seek**: Click on progress bar or drag the slider

### Playlist Management
- **Add Tracks**: Use File menu or drag & drop
- **Remove Tracks**: Right-click track → Remove, or select and press Delete
- **Reorder**: Right-click → Move Up/Down, or use Ctrl+Up/Down
- **Play Track**: Double-click any track in the playlist
- **Clear Playlist**: Right-click in playlist → Clear Playlist

### Keyboard Shortcuts
- **Spacebar**: Play/Pause
- **Ctrl+S**: Stop
- **Left/Right Arrows**: Previous/Next track
- **Up/Down Arrows**: Volume up/down
- **Ctrl+O**: Open file
- **Ctrl+Shift+O**: Open folder
- **Ctrl+P**: Toggle playlist visibility
- **F11**: Toggle fullscreen
- **Escape**: Exit fullscreen
- **Delete**: Remove selected playlist track

### View Options
- **Toggle Playlist**: View → Toggle Playlist or Ctrl+P
- **Fullscreen**: View → Fullscreen or F11
- **Menu Bar**: Hidden in fullscreen, visible in windowed mode

## Supported Audio Formats

The application supports the following audio formats:
- **MP3** (.mp3) - MPEG Audio Layer 3
- **WAV** (.wav) - Waveform Audio File Format
- **FLAC** (.flac) - Free Lossless Audio Codec
- **OGG** (.ogg) - Ogg Vorbis
- **M4A** (.m4a) - MPEG-4 Audio
- **AAC** (.aac) - Advanced Audio Coding

## Project Structure

```
AudioPlayer/
├── AudioPlayer.pro          # Qt project file
├── main.cpp                 # Application entry point
├── mainwindow.h/.cpp        # Main window class
├── audioplayer.h/.cpp       # Audio playback engine
├── playlist.h/.cpp          # Playlist management
├── playlistwidget.h/.cpp    # Playlist UI widget
├── resources.qrc            # Qt resource file
├── icons/                   # SVG icon files
│   ├── play.svg
│   ├── pause.svg
│   ├── stop.svg
│   ├── previous.svg
│   ├── next.svg
│   ├── volume.svg
│   ├── volume-low.svg
│   ├── volume-mute.svg
│   └── playlist.svg
└── README.md               # This documentation
```

## Troubleshooting

### Common Issues

**Application won't start**
- Ensure Qt 6.x is properly installed
- Check that Qt Multimedia module is available
- Verify all dependencies are met

**No audio playback**
- Check system audio settings
- Ensure audio files are in supported formats
- Verify Qt Multimedia codecs are installed

**Icons not displaying**
- Ensure resources.qrc is properly compiled
- Check that SVG files are in the icons/ directory
- Verify Qt SVG module is available

**Drag & drop not working**
- Check file permissions
- Ensure files are in supported formats
- Try using File menu as alternative

### Performance Tips
- For large playlists, consider loading files in batches
- Use FLAC for high-quality audio, MP3 for smaller file sizes
- Close other audio applications to avoid conflicts

## Contributing

This is a complete, standalone Qt application. To extend functionality:

1. **Add new audio formats**: Update supported formats lists in AudioPlayer and Playlist classes
2. **Enhance metadata**: Integrate TagLib or similar library for better metadata extraction
3. **Add visualizations**: Implement audio spectrum analyzer
4. **Improve themes**: Add multiple color schemes
5. **Add effects**: Implement audio equalizer (currently excluded by design)

## License

This project is provided as-is for educational and personal use. Modify and distribute according to your needs.

## Technical Notes

- Built with Qt 6.x Multimedia framework
- Uses QMediaPlayer for audio playback
- Implements custom dark theme using QPalette
- SVG icons for scalable, crisp graphics
- Responsive layout adapts to window resizing
- Thread-safe audio operations
- Memory-efficient playlist management
