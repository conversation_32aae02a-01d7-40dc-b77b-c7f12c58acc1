#include "audioplayer.h"
#include "playlist.h"
#include <QFileInfo>
#include <QDir>
#include <QDebug>
#include <QRegularExpression>

AudioPlayer::AudioPlayer(QObject *parent)
    : QObject(parent)
    , m_mediaPlayer(nullptr)
    , m_audioOutput(nullptr)
    , m_playlist(nullptr)
    , m_currentDuration(0)
    , m_currentVolume(0.7f)
{
    // Initialize supported formats
    m_supportedFormats << "mp3" << "wav" << "flac" << "ogg" << "m4a" << "aac";
    
    // Create media player and audio output
    m_mediaPlayer = new QMediaPlayer(this);
    m_audioOutput = new QAudioOutput(this);
    
    // Connect audio output to media player
    m_mediaPlayer->setAudioOutput(m_audioOutput);
    
    // Set initial volume
    m_audioOutput->setVolume(m_currentVolume);
    
    // Connect signals
    connect(m_mediaPlayer, &QMediaPlayer::positionChanged, this, &AudioPlayer::onPositionChanged);
    connect(m_mediaPlayer, &QMediaPlayer::durationChanged, this, &AudioPlayer::onDurationChanged);
    connect(m_mediaPlayer, &QMediaPlayer::playbackStateChanged, this, &AudioPlayer::onPlaybackStateChanged);
    connect(m_mediaPlayer, &QMediaPlayer::mediaStatusChanged, this, &AudioPlayer::onMediaStatusChanged);
    connect(m_mediaPlayer, &QMediaPlayer::errorOccurred, this, &AudioPlayer::onErrorOccurred);
}

AudioPlayer::~AudioPlayer()
{
}

void AudioPlayer::play()
{
    if (!m_currentTrackPath.isEmpty()) {
        m_mediaPlayer->play();
    }
}

void AudioPlayer::pause()
{
    m_mediaPlayer->pause();
}

void AudioPlayer::stop()
{
    m_mediaPlayer->stop();
}

void AudioPlayer::setPosition(qint64 position)
{
    m_mediaPlayer->setPosition(position);
}

void AudioPlayer::setVolume(float volume)
{
    m_currentVolume = qBound(0.0f, volume, 1.0f);
    m_audioOutput->setVolume(m_currentVolume);
}

void AudioPlayer::setCurrentTrack(const QString &filePath)
{
    if (filePath.isEmpty() || !isValidAudioFile(filePath)) {
        emit errorOccurred("Invalid audio file: " + filePath);
        return;
    }
    
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        emit errorOccurred("File not found: " + filePath);
        return;
    }
    
    m_currentTrackPath = filePath;
    
    // Set the media source
    QUrl fileUrl = QUrl::fromLocalFile(filePath);
    m_mediaPlayer->setSource(fileUrl);
    
    // Extract track information
    extractTrackInfo(filePath);
    
    emit trackChanged(filePath);
}

void AudioPlayer::setPlaylist(Playlist *playlist)
{
    m_playlist = playlist;
}

bool AudioPlayer::isPlaying() const
{
    return m_mediaPlayer->playbackState() == QMediaPlayer::PlayingState;
}

bool AudioPlayer::isPaused() const
{
    return m_mediaPlayer->playbackState() == QMediaPlayer::PausedState;
}

bool AudioPlayer::isStopped() const
{
    return m_mediaPlayer->playbackState() == QMediaPlayer::StoppedState;
}

qint64 AudioPlayer::position() const
{
    return m_mediaPlayer->position();
}

qint64 AudioPlayer::duration() const
{
    return m_mediaPlayer->duration();
}

float AudioPlayer::volume() const
{
    return m_currentVolume;
}

QString AudioPlayer::currentTrack() const
{
    return m_currentTrackPath;
}

QString AudioPlayer::getTrackTitle(const QString &filePath) const
{
    // For now, extract title from filename
    // In a full implementation, you would use a metadata library like TagLib
    return extractTitleFromFileName(filePath);
}

QString AudioPlayer::getTrackArtist(const QString &filePath) const
{
    // For now, return empty - would be extracted from metadata in full implementation
    Q_UNUSED(filePath)
    return QString();
}

qint64 AudioPlayer::getTrackDuration(const QString &filePath) const
{
    // For now, return 0 - would be extracted from metadata in full implementation
    Q_UNUSED(filePath)
    return 0;
}

void AudioPlayer::onPositionChanged(qint64 position)
{
    emit positionChanged(position);
}

void AudioPlayer::onDurationChanged(qint64 duration)
{
    m_currentDuration = duration;
    emit durationChanged(duration);
}

void AudioPlayer::onPlaybackStateChanged(QMediaPlayer::PlaybackState state)
{
    Q_UNUSED(state)
    emit playbackStateChanged();
}

void AudioPlayer::onMediaStatusChanged(QMediaPlayer::MediaStatus status)
{
    Q_UNUSED(status)
    emit mediaStatusChanged();
}

void AudioPlayer::onErrorOccurred(QMediaPlayer::Error error, const QString &errorString)
{
    Q_UNUSED(error)
    emit errorOccurred(errorString);
}

void AudioPlayer::extractTrackInfo(const QString &filePath)
{
    // Extract basic info from filename
    m_currentTitle = extractTitleFromFileName(filePath);
    m_currentArtist = QString(); // Would be extracted from metadata
    
    emit trackInfoChanged(m_currentTitle, m_currentArtist, m_currentDuration);
}

QString AudioPlayer::extractTitleFromFileName(const QString &filePath) const
{
    QFileInfo fileInfo(filePath);
    QString baseName = fileInfo.baseName();
    
    // Remove common patterns like "01 - " or "01. "
    QRegularExpression trackNumberPattern("^\\d+[\\s\\-\\.]+");
    baseName.remove(trackNumberPattern);
    
    // Replace underscores with spaces
    baseName.replace('_', ' ');
    
    return baseName.isEmpty() ? fileInfo.baseName() : baseName;
}

bool AudioPlayer::isValidAudioFile(const QString &filePath) const
{
    QFileInfo fileInfo(filePath);
    QString suffix = fileInfo.suffix().toLower();
    return m_supportedFormats.contains(suffix);
}
