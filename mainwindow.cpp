#include "mainwindow.h"
#include "audioplayer.h"
#include "playlistwidget.h"
#include <QApplication>
#include <QMessageBox>
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QKeyEvent>
#include <QResizeEvent>
#include <QPixmap>
#include <QDir>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainSplitter(nullptr)
    , m_controlsWidget(nullptr)
    , m_playlistWidget(nullptr)
    , m_playPauseButton(nullptr)
    , m_stopButton(nullptr)
    , m_previousButton(nullptr)
    , m_nextButton(nullptr)
    , m_volumeButton(nullptr)
    , m_playlistButton(nullptr)
    , m_volumeSlider(nullptr)
    , m_positionSlider(nullptr)
    , m_progressBar(nullptr)
    , m_albumArtLabel(nullptr)
    , m_trackTitleLabel(nullptr)
    , m_trackArtist<PERSON>abel(nullptr)
    , m_currentTimeLabel(nullptr)
    , m_totalTimeLabel(nullptr)
    , m_volumeLabel(nullptr)
    , m_audioPlayer(nullptr)
    , m_playlistVisible(true)
    , m_isFullscreen(false)
    , m_duration(0)
    , m_positionSliderPressed(false)
{
    setWindowTitle("Audio Player");
    setMinimumSize(800, 600);
    resize(1200, 800);
    
    // Enable drag and drop
    setAcceptDrops(true);
    
    // Initialize components
    m_audioPlayer = new AudioPlayer(this);
    m_playlistWidget = new PlaylistWidget(this);
    
    setupUI();
    setupMenuBar();
    setupShortcuts();
    
    // Connect audio player signals
    connect(m_audioPlayer, &AudioPlayer::positionChanged, this, &MainWindow::updatePosition);
    connect(m_audioPlayer, &AudioPlayer::durationChanged, this, &MainWindow::updateDuration);
    connect(m_audioPlayer, &AudioPlayer::trackInfoChanged, this, &MainWindow::updateTrackInfo);
    connect(m_audioPlayer, &AudioPlayer::playbackStateChanged, this, &MainWindow::onPlaybackStateChanged);
    connect(m_audioPlayer, &AudioPlayer::mediaStatusChanged, this, &MainWindow::onMediaStatusChanged);
    connect(m_audioPlayer, &AudioPlayer::errorOccurred, this, &MainWindow::showError);
    
    // Connect playlist signals
    connect(m_playlistWidget, &PlaylistWidget::trackSelected, m_audioPlayer, &AudioPlayer::setCurrentTrack);
    connect(m_playlistWidget, &PlaylistWidget::playTrack, m_audioPlayer, &AudioPlayer::play);
    
    // Connect audio player to playlist
    connect(m_audioPlayer, &AudioPlayer::trackChanged, m_playlistWidget, &PlaylistWidget::setCurrentTrack);
    
    // Initialize UI state
    updatePlayPauseButton();
    updateTimeLabels();
    setDefaultAlbumArt();

    // Set initial volume
    m_volumeSlider->setValue(70);
    m_audioPlayer->setVolume(0.7f);
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);
    
    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    
    // Create controls widget
    m_controlsWidget = new QWidget;
    m_controlsWidget->setMinimumWidth(400);
    
    setupControlsLayout();
    
    // Add widgets to splitter
    m_mainSplitter->addWidget(m_controlsWidget);
    m_mainSplitter->addWidget(m_playlistWidget);
    m_mainSplitter->setStretchFactor(0, 1);
    m_mainSplitter->setStretchFactor(1, 1);
    
    // Main layout
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    m_mainLayout->addWidget(m_mainSplitter);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    
    // Status bar
    statusBar()->showMessage("Ready");
}

void MainWindow::setupControlsLayout()
{
    QVBoxLayout *layout = new QVBoxLayout(m_controlsWidget);
    layout->setSpacing(20);
    layout->setContentsMargins(20, 20, 20, 20);

    // Album art section
    m_albumArtLabel = new QLabel;
    m_albumArtLabel->setFixedSize(200, 200);
    m_albumArtLabel->setAlignment(Qt::AlignCenter);
    m_albumArtLabel->setStyleSheet(
        "QLabel {"
        "    border: 2px solid #555555;"
        "    border-radius: 10px;"
        "    background-color: #2a2a2a;"
        "}"
    );
    m_albumArtLabel->setScaledContents(true);
    setDefaultAlbumArt();

    QHBoxLayout *albumArtLayout = new QHBoxLayout;
    albumArtLayout->addStretch();
    albumArtLayout->addWidget(m_albumArtLabel);
    albumArtLayout->addStretch();
    layout->addLayout(albumArtLayout);

    // Track info section
    QVBoxLayout *trackInfoLayout = new QVBoxLayout;
    m_trackTitleLabel = new QLabel("No track selected");
    m_trackTitleLabel->setStyleSheet("font-size: 18px; font-weight: bold; color: white;");
    m_trackTitleLabel->setAlignment(Qt::AlignCenter);
    
    m_trackArtistLabel = new QLabel("");
    m_trackArtistLabel->setStyleSheet("font-size: 14px; color: #cccccc;");
    m_trackArtistLabel->setAlignment(Qt::AlignCenter);
    
    trackInfoLayout->addWidget(m_trackTitleLabel);
    trackInfoLayout->addWidget(m_trackArtistLabel);
    layout->addLayout(trackInfoLayout);
    
    // Progress section
    QVBoxLayout *progressLayout = new QVBoxLayout;
    m_positionSlider = new QSlider(Qt::Horizontal);
    m_positionSlider->setMinimum(0);
    m_positionSlider->setMaximum(100);
    m_positionSlider->setValue(0);
    m_positionSlider->setStyleSheet(
        "QSlider::groove:horizontal { border: 1px solid #999999; height: 8px; background: #333333; margin: 2px 0; border-radius: 4px; }"
        "QSlider::handle:horizontal { background: #42a5f5; border: 1px solid #1976d2; width: 18px; margin: -2px 0; border-radius: 9px; }"
        "QSlider::sub-page:horizontal { background: #42a5f5; border: 1px solid #999999; height: 8px; border-radius: 4px; }"
    );
    
    connect(m_positionSlider, &QSlider::sliderPressed, [this]() { m_positionSliderPressed = true; });
    connect(m_positionSlider, &QSlider::sliderReleased, [this]() { 
        m_positionSliderPressed = false;
        seekToPosition(m_positionSlider->value());
    });
    
    // Time labels
    m_timeLayout = new QHBoxLayout;
    m_currentTimeLabel = new QLabel("00:00");
    m_totalTimeLabel = new QLabel("00:00");
    m_currentTimeLabel->setStyleSheet("color: #cccccc;");
    m_totalTimeLabel->setStyleSheet("color: #cccccc;");
    
    m_timeLayout->addWidget(m_currentTimeLabel);
    m_timeLayout->addStretch();
    m_timeLayout->addWidget(m_totalTimeLabel);
    
    progressLayout->addWidget(m_positionSlider);
    progressLayout->addLayout(m_timeLayout);
    layout->addLayout(progressLayout);
    
    // Control buttons
    setupControlButtons();
    layout->addLayout(m_buttonLayout);
    
    // Volume control
    setupVolumeControl();
    layout->addLayout(m_volumeLayout);
    
    layout->addStretch();
}

void MainWindow::setupControlButtons()
{
    m_buttonLayout = new QGridLayout;
    m_buttonLayout->setSpacing(10);
    
    // Create buttons
    m_previousButton = new QPushButton;
    m_playPauseButton = new QPushButton;
    m_stopButton = new QPushButton;
    m_nextButton = new QPushButton;
    m_playlistButton = new QPushButton;
    
    // Set button properties
    QList<QPushButton*> buttons = {m_previousButton, m_playPauseButton, m_stopButton, m_nextButton, m_playlistButton};
    for (auto button : buttons) {
        button->setFixedSize(60, 60);
        button->setStyleSheet(
            "QPushButton { background-color: #404040; border: 2px solid #606060; border-radius: 30px; }"
            "QPushButton:hover { background-color: #505050; border-color: #42a5f5; }"
            "QPushButton:pressed { background-color: #303030; }"
        );
    }
    
    // Set icons (will be implemented with SVG resources)
    setButtonIcon(m_previousButton, "previous");
    setButtonIcon(m_playPauseButton, "play");
    setButtonIcon(m_stopButton, "stop");
    setButtonIcon(m_nextButton, "next");
    setButtonIcon(m_playlistButton, "playlist");
    
    // Connect signals
    connect(m_previousButton, &QPushButton::clicked, this, &MainWindow::previousTrack);
    connect(m_playPauseButton, &QPushButton::clicked, this, &MainWindow::togglePlayPause);
    connect(m_stopButton, &QPushButton::clicked, this, &MainWindow::stopPlayback);
    connect(m_nextButton, &QPushButton::clicked, this, &MainWindow::nextTrack);
    connect(m_playlistButton, &QPushButton::clicked, this, &MainWindow::togglePlaylist);
    
    // Layout buttons
    m_buttonLayout->addWidget(m_previousButton, 0, 0, Qt::AlignCenter);
    m_buttonLayout->addWidget(m_playPauseButton, 0, 1, Qt::AlignCenter);
    m_buttonLayout->addWidget(m_stopButton, 0, 2, Qt::AlignCenter);
    m_buttonLayout->addWidget(m_nextButton, 0, 3, Qt::AlignCenter);
    m_buttonLayout->addWidget(m_playlistButton, 1, 1, 1, 2, Qt::AlignCenter);
}

void MainWindow::setupVolumeControl()
{
    m_volumeLayout = new QHBoxLayout;
    
    m_volumeButton = new QPushButton;
    m_volumeButton->setFixedSize(40, 40);
    m_volumeButton->setStyleSheet(
        "QPushButton { background-color: #404040; border: 2px solid #606060; border-radius: 20px; }"
        "QPushButton:hover { background-color: #505050; border-color: #42a5f5; }"
    );
    setButtonIcon(m_volumeButton, "volume");
    
    m_volumeSlider = new QSlider(Qt::Horizontal);
    m_volumeSlider->setMinimum(0);
    m_volumeSlider->setMaximum(100);
    m_volumeSlider->setValue(70);
    m_volumeSlider->setStyleSheet(
        "QSlider::groove:horizontal { border: 1px solid #999999; height: 6px; background: #333333; margin: 2px 0; border-radius: 3px; }"
        "QSlider::handle:horizontal { background: #42a5f5; border: 1px solid #1976d2; width: 16px; margin: -2px 0; border-radius: 8px; }"
        "QSlider::sub-page:horizontal { background: #42a5f5; border: 1px solid #999999; height: 6px; border-radius: 3px; }"
    );
    
    m_volumeLabel = new QLabel("70%");
    m_volumeLabel->setStyleSheet("color: #cccccc;");
    m_volumeLabel->setMinimumWidth(40);
    
    connect(m_volumeSlider, &QSlider::valueChanged, this, &MainWindow::setVolume);
    
    m_volumeLayout->addWidget(m_volumeButton);
    m_volumeLayout->addWidget(m_volumeSlider);
    m_volumeLayout->addWidget(m_volumeLabel);
}

void MainWindow::setupMenuBar()
{
    // File menu
    QMenu *fileMenu = menuBar()->addMenu("&File");

    m_openFileAction = new QAction("&Open File...", this);
    m_openFileAction->setShortcut(QKeySequence::Open);
    connect(m_openFileAction, &QAction::triggered, this, &MainWindow::openFile);
    fileMenu->addAction(m_openFileAction);

    m_openFolderAction = new QAction("Open &Folder...", this);
    m_openFolderAction->setShortcut(QKeySequence("Ctrl+Shift+O"));
    connect(m_openFolderAction, &QAction::triggered, this, &MainWindow::openFolder);
    fileMenu->addAction(m_openFolderAction);

    fileMenu->addSeparator();

    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    fileMenu->addAction(m_exitAction);

    // View menu
    QMenu *viewMenu = menuBar()->addMenu("&View");

    m_togglePlaylistAction = new QAction("Toggle &Playlist", this);
    m_togglePlaylistAction->setShortcut(QKeySequence("Ctrl+P"));
    m_togglePlaylistAction->setCheckable(true);
    m_togglePlaylistAction->setChecked(true);
    connect(m_togglePlaylistAction, &QAction::triggered, this, &MainWindow::togglePlaylist);
    viewMenu->addAction(m_togglePlaylistAction);

    m_fullscreenAction = new QAction("&Fullscreen", this);
    m_fullscreenAction->setShortcut(QKeySequence::FullScreen);
    connect(m_fullscreenAction, &QAction::triggered, this, &MainWindow::toggleFullscreen);
    viewMenu->addAction(m_fullscreenAction);
}

void MainWindow::setupShortcuts()
{
    m_playPauseShortcut = new QShortcut(QKeySequence(Qt::Key_Space), this);
    connect(m_playPauseShortcut, &QShortcut::activated, this, &MainWindow::togglePlayPause);

    m_stopShortcut = new QShortcut(QKeySequence("Ctrl+S"), this);
    connect(m_stopShortcut, &QShortcut::activated, this, &MainWindow::stopPlayback);

    m_nextShortcut = new QShortcut(QKeySequence(Qt::Key_Right), this);
    connect(m_nextShortcut, &QShortcut::activated, this, &MainWindow::nextTrack);

    m_previousShortcut = new QShortcut(QKeySequence(Qt::Key_Left), this);
    connect(m_previousShortcut, &QShortcut::activated, this, &MainWindow::previousTrack);

    m_volumeUpShortcut = new QShortcut(QKeySequence(Qt::Key_Up), this);
    connect(m_volumeUpShortcut, &QShortcut::activated, [this]() {
        int newVolume = qMin(100, m_volumeSlider->value() + 5);
        m_volumeSlider->setValue(newVolume);
    });

    m_volumeDownShortcut = new QShortcut(QKeySequence(Qt::Key_Down), this);
    connect(m_volumeDownShortcut, &QShortcut::activated, [this]() {
        int newVolume = qMax(0, m_volumeSlider->value() - 5);
        m_volumeSlider->setValue(newVolume);
    });
}

void MainWindow::dragEnterEvent(QDragEnterEvent *event)
{
    if (event->mimeData()->hasUrls()) {
        bool hasAudioFiles = false;
        for (const QUrl &url : event->mimeData()->urls()) {
            if (url.isLocalFile()) {
                QString fileName = url.toLocalFile();
                QFileInfo fileInfo(fileName);
                QString suffix = fileInfo.suffix().toLower();
                if (suffix == "mp3" || suffix == "wav" || suffix == "flac" ||
                    suffix == "ogg" || suffix == "m4a" || suffix == "aac") {
                    hasAudioFiles = true;
                    break;
                }
            }
        }
        if (hasAudioFiles) {
            event->acceptProposedAction();
        }
    }
}

void MainWindow::dropEvent(QDropEvent *event)
{
    QStringList audioFiles;
    for (const QUrl &url : event->mimeData()->urls()) {
        if (url.isLocalFile()) {
            QString fileName = url.toLocalFile();
            QFileInfo fileInfo(fileName);
            QString suffix = fileInfo.suffix().toLower();
            if (suffix == "mp3" || suffix == "wav" || suffix == "flac" ||
                suffix == "ogg" || suffix == "m4a" || suffix == "aac") {
                audioFiles.append(fileName);
            }
        }
    }

    if (!audioFiles.isEmpty()) {
        m_playlistWidget->addFiles(audioFiles);
        event->acceptProposedAction();
    }
}

void MainWindow::keyPressEvent(QKeyEvent *event)
{
    // Handle escape key for fullscreen
    if (event->key() == Qt::Key_Escape && m_isFullscreen) {
        toggleFullscreen();
        return;
    }

    QMainWindow::keyPressEvent(event);
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);
    adaptToFullscreen();
}

// Slot implementations
void MainWindow::openFile()
{
    QString fileName = QFileDialog::getOpenFileName(this,
        "Open Audio File",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation),
        "Audio Files (*.mp3 *.wav *.flac *.ogg *.m4a *.aac);;All Files (*)");

    if (!fileName.isEmpty()) {
        m_playlistWidget->addFile(fileName);
    }
}

void MainWindow::openFolder()
{
    QString folderPath = QFileDialog::getExistingDirectory(this,
        "Open Audio Folder",
        QStandardPaths::writableLocation(QStandardPaths::MusicLocation));

    if (!folderPath.isEmpty()) {
        m_playlistWidget->addFolder(folderPath);
    }
}

void MainWindow::togglePlayPause()
{
    if (m_audioPlayer->isPlaying()) {
        m_audioPlayer->pause();
    } else {
        m_audioPlayer->play();
    }
}

void MainWindow::stopPlayback()
{
    m_audioPlayer->stop();
    // When stopped, show default album art
    setDefaultAlbumArt();
}

void MainWindow::nextTrack()
{
    m_playlistWidget->playNext();
}

void MainWindow::previousTrack()
{
    m_playlistWidget->playPrevious();
}

void MainWindow::setVolume(int volume)
{
    float volumeFloat = volume / 100.0f;
    m_audioPlayer->setVolume(volumeFloat);
    m_volumeLabel->setText(QString("%1%").arg(volume));

    // Update volume button icon based on level
    if (volume == 0) {
        setButtonIcon(m_volumeButton, "volume-mute");
    } else if (volume < 50) {
        setButtonIcon(m_volumeButton, "volume-low");
    } else {
        setButtonIcon(m_volumeButton, "volume");
    }
}

void MainWindow::seekToPosition(int position)
{
    if (m_duration > 0) {
        qint64 seekPosition = (position * m_duration) / 100;
        m_audioPlayer->setPosition(seekPosition);
    }
}

void MainWindow::updatePosition(qint64 position)
{
    if (!m_positionSliderPressed && m_duration > 0) {
        int sliderPosition = (position * 100) / m_duration;
        m_positionSlider->setValue(sliderPosition);
    }

    m_currentTimeLabel->setText(formatTime(position));
}

void MainWindow::updateDuration(qint64 duration)
{
    m_duration = duration;
    m_totalTimeLabel->setText(formatTime(duration));
}

void MainWindow::updateTrackInfo(const QString &title, const QString &artist, qint64 duration)
{
    m_trackTitleLabel->setText(title.isEmpty() ? "Unknown Title" : title);
    m_trackArtistLabel->setText(artist.isEmpty() ? "Unknown Artist" : artist);

    QString statusText = QString("%1 - %2").arg(artist.isEmpty() ? "Unknown Artist" : artist,
                                                title.isEmpty() ? "Unknown Title" : title);
    statusBar()->showMessage(statusText);

    // Update album art when track info changes
    updateAlbumArt(m_audioPlayer->currentTrack());
}

void MainWindow::togglePlaylist()
{
    m_playlistVisible = !m_playlistVisible;
    m_playlistWidget->setVisible(m_playlistVisible);
    m_togglePlaylistAction->setChecked(m_playlistVisible);
}

void MainWindow::toggleFullscreen()
{
    if (m_isFullscreen) {
        showNormal();
        menuBar()->show();
        statusBar()->show();
        m_isFullscreen = false;
    } else {
        showFullScreen();
        menuBar()->hide();
        statusBar()->hide();
        m_isFullscreen = true;
    }
    adaptToFullscreen();
}

void MainWindow::onPlaybackStateChanged()
{
    updatePlayPauseButton();
}

void MainWindow::onMediaStatusChanged()
{
    // Handle media status changes if needed
}

void MainWindow::showError(const QString &error)
{
    QMessageBox::warning(this, "Audio Player Error", error);
    statusBar()->showMessage("Error: " + error, 5000);
}

// Utility methods
void MainWindow::updatePlayPauseButton()
{
    if (m_audioPlayer->isPlaying()) {
        setButtonIcon(m_playPauseButton, "pause");
    } else {
        setButtonIcon(m_playPauseButton, "play");
    }
}

void MainWindow::updateTimeLabels()
{
    // This method can be used for additional time label updates if needed
}

void MainWindow::setButtonIcon(QPushButton *button, const QString &iconName)
{
    QString iconPath = QString(":/icons/icons/%1.svg").arg(iconName);
    QIcon icon(iconPath);
    button->setIcon(icon);
    button->setIconSize(QSize(24, 24));
    button->setText(""); // Clear any text
}

QString MainWindow::formatTime(qint64 milliseconds)
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

void MainWindow::adaptToFullscreen()
{
    if (m_isFullscreen) {
        // Adjust layout for fullscreen mode
        m_mainLayout->setContentsMargins(20, 20, 20, 20);
        if (m_playlistVisible) {
            m_mainSplitter->setSizes({1, 1});
        }
    } else {
        // Normal window mode
        m_mainLayout->setContentsMargins(10, 10, 10, 10);
    }
}

void MainWindow::updateAlbumArt(const QString &filePath)
{
    if (filePath.isEmpty()) {
        setDefaultAlbumArt();
        return;
    }

    // Try to find album art
    QString albumArtPath = findAlbumArt(filePath);

    if (!albumArtPath.isEmpty()) {
        QPixmap albumArt(albumArtPath);
        if (!albumArt.isNull()) {
            // Scale the image to fit the label while maintaining aspect ratio
            QPixmap scaledPixmap = albumArt.scaled(m_albumArtLabel->size(),
                                                  Qt::KeepAspectRatio,
                                                  Qt::SmoothTransformation);
            m_albumArtLabel->setPixmap(scaledPixmap);
            return;
        }
    }

    // If no album art found or failed to load, use default
    setDefaultAlbumArt();
}

void MainWindow::setDefaultAlbumArt()
{
    QPixmap defaultArt(":/icons/icons/album-default.svg");
    if (!defaultArt.isNull()) {
        QPixmap scaledPixmap = defaultArt.scaled(m_albumArtLabel->size(),
                                                Qt::KeepAspectRatio,
                                                Qt::SmoothTransformation);
        m_albumArtLabel->setPixmap(scaledPixmap);
    } else {
        // Fallback if SVG fails to load
        m_albumArtLabel->setText("♪");
        m_albumArtLabel->setStyleSheet(m_albumArtLabel->styleSheet() +
                                      " font-size: 48px; color: #42a5f5;");
    }
}

QString MainWindow::findAlbumArt(const QString &audioFilePath)
{
    QFileInfo audioFile(audioFilePath);
    QString audioDir = audioFile.absolutePath();
    QString baseName = audioFile.baseName();

    // Common album art filenames to search for
    QStringList albumArtNames = {
        "cover.jpg", "cover.jpeg", "cover.png",
        "album.jpg", "album.jpeg", "album.png",
        "folder.jpg", "folder.jpeg", "folder.png",
        "front.jpg", "front.jpeg", "front.png",
        baseName + ".jpg", baseName + ".jpeg", baseName + ".png"
    };

    // Search for album art in the same directory as the audio file
    for (const QString &artName : albumArtNames) {
        QString artPath = QDir(audioDir).absoluteFilePath(artName);
        if (QFile::exists(artPath)) {
            return artPath;
        }
    }

    // Search for any image file in the directory (first found)
    QDir dir(audioDir);
    QStringList imageFilters = {"*.jpg", "*.jpeg", "*.png", "*.bmp", "*.gif"};
    QStringList imageFiles = dir.entryList(imageFilters, QDir::Files);

    if (!imageFiles.isEmpty()) {
        return dir.absoluteFilePath(imageFiles.first());
    }

    return QString(); // No album art found
}
